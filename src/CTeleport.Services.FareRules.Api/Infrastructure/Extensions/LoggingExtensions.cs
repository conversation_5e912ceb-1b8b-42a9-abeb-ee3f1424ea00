using CTeleport.Common.Logging.Extensions;
using CTeleport.Common.Sentry;
using CTeleport.Infrastructure;
using CTeleport.Infrastructure.Http;
using CTeleport.Infrastructure.RawRabbit;
using CTeleport.Infrastructure.Serilog;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using Serilog.Extensions.Hosting;

namespace CTeleport.Services.FareRules.Api.Infrastructure.Extensions;

public static class LoggingExtensions
{
    public static IApplicationBuilder UseLogging(this IApplicationBuilder app)
    {
        app.UseSerilogRequestLogging(c => c.GetLevel = LogHelper.GetRequestLoggingLevel);
        app.UseTraceContext();
        return app;
    }

    public static IHostBuilder UseSerilog(this IHostBuilder hostBuilder)
    {
        hostBuilder.UseLogging(c => c.Enrich.WithTraceContext());
        return hostBuilder;
    }

    public static IServiceCollection AddLogging(this IServiceCollection services, IConfiguration config)
    {
        services.AddSentry(config, AssemblyHelper.GetSolutionName());
        services.AddLogging(lb => lb.AddSerilog());
        services.AddTraceContext();
        services.AddTraceparentHeaderPropagation();
        services.AddRawRabbitTraceContextProvider();
        services.AddSingleton(new DiagnosticContext(Log.Logger));

        return services;
    }
}
