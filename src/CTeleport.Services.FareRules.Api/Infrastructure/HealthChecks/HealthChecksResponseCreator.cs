using System.Linq;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace CTeleport.Services.FareRules.Api.Infrastructure.HealthChecks;

public static class HealthChecksResponseCreator
{
    public static HealthChecksResponse Create(HealthReport healthReport)
    {
        var response = new HealthChecksResponse();

        var status = CalculateServiceHealthStatus(healthReport);

        response.Status = status.ToString();
        response.TotalResponseTime = healthReport.TotalDuration.TotalMilliseconds;

        response.Checks = healthReport.Entries.Select(e =>
            new HealthCheck
            {
                Name = e.Key,
                Status = e.Value.Status.ToString(),
                ResponseTime = e.Value.Duration.TotalMilliseconds
            })
            .ToList();

        return response;
    }

    private static HealthStatus CalculateServiceHealthStatus(HealthReport healthReport)
    {
        if (healthReport.Entries.TryGetValue(HealthChecksNames.Cache, out var cacheCheckEntry)
            && cacheCheckEntry.Status == HealthStatus.Unhealthy)
        {
            return HealthStatus.Unhealthy;
        }

        return cacheCheckEntry.Status != HealthStatus.Healthy
            ? HealthStatus.Degraded
            : HealthStatus.Healthy;
    }
}
