using System.Threading;
using System.Threading.Tasks;
using CTeleport.Services.FareRules.Service.Database;
using Microsoft.Extensions.Hosting;

namespace CTeleport.Services.FareRules.Api.HostedServices;

public class DatabaseSeedHostedService : IHostedService
{
    private readonly IDatabaseSeeder _databaseSeeder;

    public DatabaseSeedHostedService(IDatabaseSeeder databaseSeeder) => _databaseSeeder = databaseSeeder;

    public async Task StartAsync(CancellationToken cancellationToken) => await _databaseSeeder.SeedAsync();

    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
}