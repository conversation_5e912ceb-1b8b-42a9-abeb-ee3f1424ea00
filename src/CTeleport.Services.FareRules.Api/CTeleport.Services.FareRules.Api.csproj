<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <AssemblyTitle>CTeleport.Services.FareRules.Api</AssemblyTitle>
        <AssemblyName>CTeleport.Services.FareRules.Api</AssemblyName>
        <PackageId>CTeleport.Services.FareRules.Api</PackageId>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AspNetCore.HealthChecks.Redis" Version="8.0.1" />
        <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="8.0.0" />
        <PackageReference Include="AutoMapper" Version="12.0.1" />
        <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
        <PackageReference Include="CTeleport.Common.Apm" Version="1.1.50" />
        <PackageReference Include="CTeleport.Common.Logging" Version="1.1.110" />
        <PackageReference Include="CTeleport.HealthChecks.Core" Version="2024.11.25.27" />
        <PackageReference Include="CTeleport.Infrastructure" Version="2024.11.22.53" />
        <PackageReference Include="CTeleport.Infrastructure.Http" Version="2024.11.22.53" />
        <PackageReference Include="CTeleport.Infrastructure.RawRabbit" Version="2024.11.22.53" />
        <PackageReference Include="CTeleport.Infrastructure.Serilog" Version="2024.11.22.53" />
        <PackageReference Include="CTeleport.Messages" Version="1.1.3431" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.2" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.6" NoWarn="NU1605" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.6" NoWarn="NU1605" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.6" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.6" />
        <PackageReference Include="NJsonSchema" Version="10.9.0" />
        <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.5.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\CTeleport.Services.FareRules.Service\CTeleport.Services.FareRules.Service.csproj" />
    </ItemGroup>

    <Target Name="CopyPackages" AfterTargets="Build">
      <ItemGroup>
        <PackageReferenceFiles Condition="%(PackageReference.CopyToOutputDirectory) != ''" Include="$(NugetPackageRoot)\%(PackageReference.CopyToOutputDirectory)\%(PackageReference.Identity).xml" />
      </ItemGroup>
      <Copy SourceFiles="@(PackageReferenceFiles)" DestinationFolder="$(OutDir)" />
    </Target>

    <Target Name="CopyPackagess" BeforeTargets="PrepareForPublish">
      <ItemGroup>
        <PackageReferenceFiles Condition="%(PackageReference.CopyToOutputDirectory) != ''" Include="$(NugetPackageRoot)\%(PackageReference.CopyToOutputDirectory)\%(PackageReference.Identity).xml" />
      </ItemGroup>
      <Copy SourceFiles="@(PackageReferenceFiles)" DestinationFolder="$(PublishDir)" />
    </Target>

</Project>
