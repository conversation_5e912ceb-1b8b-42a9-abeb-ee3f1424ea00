using Autofac.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Threading.Tasks;
using Autofac;
using CTeleport.Services.FareRules.Api.Infrastructure;
using CTeleport.Services.FareRules.Api.Infrastructure.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;

namespace CTeleport.Services.FareRules.Api;

[SuppressMessage("AsyncUsage.CSharp.Naming", "UseAsyncSuffix:Use Async suffix")]
public class Program
{
    public static async Task<int> Main(string[] args)
    {
        try
        {
            var builder = WebApplication.CreateBuilder(new WebApplicationOptions
            {
                ApplicationName = typeof(Program).Assembly.FullName,
                ContentRootPath = Directory.GetCurrentDirectory()
            });

            var config = builder.Configuration;
            var startup = new Startup(builder.Configuration);

            builder.Host
                .ConfigureHost(config)
                .ConfigureServices((_, services) => { services.AddHttpClient(); })
                .UseServiceProviderFactory(new AutofacServiceProviderFactory())
                .UseSerilog()
                .ConfigureContainer<ContainerBuilder>(startup.ConfigureContainer);

            startup.ConfigureServices(builder.Services);

            var app = builder.Build();

            startup.Configure(app, app.Environment);

            await app.RunAsync();

            return 0;
        }
        catch (Exception ex)
        {
            await ServiceBootstrapExceptionHandler.PrintExceptionAsync(ex);
            return -1;
        }
        finally
        {
            await Log.CloseAndFlushAsync();
        }
    }
}
