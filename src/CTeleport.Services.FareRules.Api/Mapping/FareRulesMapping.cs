using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using CTeleport.FareRules.Shared.Dto;
using CTeleport.FareRules.Shared.Models;
using CTeleport.Messages.Commands.Models;
using CTeleport.Services.FareRules.Api.Models;
using CTeleport.Services.FareRules.Service.Helpers;
using CTeleport.Services.FareRules.Service.Repositories.Models;
using CTeleport.Services.Search.Shared.Models;

namespace CTeleport.Services.FareRules.Api.Mapping;

public class FareRulesMapping: Profile
{
    public FareRulesMapping()
    {
        CreateMap<FareRule16, FareRule16Entity>()
            .ForMember(e => e.HasSimilarValidatedChangesRule, e => e.Ignore())
            .ForMember(e => e.HasSimilarValidatedRefundsRule, e => e.Ignore())
            .ReverseMap();
            
        CreateMap<FareRule16Entity, BaseFareRule16Entity>()
            .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy.Name))
            .ForMember(dest => dest.UpdatedBy, opt => opt.MapFrom(src => src.UpdatedBy == null ? null : src.UpdatedBy.Name))
            .ReverseMap();

        CreateMap<FareRule16Entity, FareRule16Dto>()
            .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy.Name))
            .ForMember(dest => dest.UpdatedBy, opt => opt.MapFrom(src => src.UpdatedBy == null ? null : src.UpdatedBy.Name))
            .ForMember(dest => dest.RawText, opt => opt.MapFrom(src => src.RawText))
            .ReverseMap();

        CreateMap<FareRule16UpdateRequest, FareRule16Entity>()
            .ForMember(dest => dest.RawText, opt => opt.Ignore())
            .ForMember(dest => dest.NormalizedText, opt => opt.Ignore())
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.ReservationsCount, opt => opt.Ignore())
            .ForMember(dest => dest.CancellationsCount, opt => opt.Ignore())
            .ForMember(dest => dest.ChangesCount, opt => opt.Ignore())
            .ForMember(dest => dest.LatestReservation, opt => opt.Ignore())
            .ForMember(dest => dest.PlatingCarriers, opt => opt.Ignore())
            .ForMember(dest => dest.ValidationFinished, opt => opt.Ignore())
            .ForMember(dest => dest.ShouldBeRevalidated, opt => opt.Ignore())
            .ForMember(dest => dest.WasValidated, opt => opt.Ignore())
            .ForMember(e => e.HasSimilarValidatedChangesRule, e => e.Ignore())
            .ForMember(e => e.HasSimilarValidatedRefundsRule, e => e.Ignore());

        CreateMap<FareRuleSectionDto, FareRuleSectionEntity>()
            .ForMember(dest => dest.Category, opt => opt.MapFrom(src => src.Category))
            .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.Title))
            .ForMember(dest => dest.Text, opt => opt.MapFrom(src => src.Text))
            .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest) =>
                string.IsNullOrEmpty(src.Id) 
                    ? FareRulesHelper.GetFareRuleIdBySection(new FareRuleSection
                      {
                          Category = src.Category, Title = src.Title, Text = src.Text
                      })
                    : src.Id));

        CreateMap<FareRuleSectionEntity, FareRuleSectionDto>();
        
        CreateMap<FareRulesSectionsRequest, List<FareRuleSectionEntity>>()
            .ConvertUsing((src, _, context) => 
                src.Sections.Select(section => context.Mapper.Map<FareRuleSectionEntity>(section)).ToList());
        
        CreateMap<FareRuleSectionData, FareRuleSectionEntity>();
    }
}
