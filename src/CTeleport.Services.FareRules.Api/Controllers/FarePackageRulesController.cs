using System;
using System.Threading.Tasks;
using CTeleport.Common.Authorization;
using CTeleport.FareRules.Shared.Dto;
using CTeleport.Services.FareRules.Service.Contracts;
using CTeleport.Services.FareRules.Service.Services.Interfaces;
using CTeleport.Services.FareRules.Service.Types;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;

namespace CTeleport.Services.FareRules.Api.Controllers;

[ApiController]
[Route("fare-package-rules")]
public class FarePackageRulesController : ControllerBase
{
    private readonly IFarePackageRulesService _farePackageRulesService;

    public FarePackageRulesController(IFarePackageRulesService farePackageRulesService)
    {
        _farePackageRulesService = farePackageRulesService;
    }

    [HttpGet]
    [ApiAuthorize(AuthScopes.FarePackagesRead)]
    [ProducesResponseType(typeof(PaginationResponse<FarePackageRuleDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetPageAsync([FromQuery] PaginationRequest request)
    {
        var result = await _farePackageRulesService.GetPageAsync(request);
        return result.Match<IActionResult>(Ok,
            error => error switch
            {
                ValidationError validation => ValidationProblem(new ValidationProblemDetails(validation.Errors)),
                _ => Problem(error.Message, statusCode: StatusCodes.Status500InternalServerError)
            });
    }

    [HttpGet("{id:guid}")]
    [ApiAuthorize(AuthScopes.FarePackagesRead)]
    [ProducesResponseType(typeof(FarePackageRuleDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(NotFoundResult), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetByIdAsync([FromRoute] Guid id)
    {
        var result = await _farePackageRulesService.GetById(id);
        return result.Match<IActionResult>(Ok,
            error => error switch
            {
                ValidationError validation => ValidationProblem(new ValidationProblemDetails(validation.Errors)),
                NotFoundError => NotFound(),
                _ => Problem(error.Message, statusCode: StatusCodes.Status500InternalServerError)
            });
    }

    [HttpPost]
    [ApiAuthorize(AuthScopes.FarePackagesCreate)]
    [ProducesResponseType(typeof(Created<FarePackageRuleDto>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status409Conflict)]
    public async Task<IActionResult> CreateAsync([FromBody] CreateFarePackageRuleRequest request)
    {
        var result = await _farePackageRulesService.CreateAsync(request);
        return result.Match<IActionResult>(Ok,
            error => error switch
            {
                ValidationError validation => ValidationProblem(new ValidationProblemDetails(validation.Errors)),
                DuplicateFoundError => Problem(error.Message, statusCode: StatusCodes.Status409Conflict),
                _ => Problem(error.Message, statusCode: StatusCodes.Status500InternalServerError)
            });
    }

    [HttpPut("{id:guid}")]
    [ApiAuthorize(AuthScopes.FarePackagesManage)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(NotFoundResult), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status409Conflict)]
    [ProducesResponseType(typeof(FarePackageRuleDto), StatusCodes.Status200OK)]
    public async Task<IActionResult> UpdateAsync([FromRoute] Guid id, [FromBody] UpdateFarePackageRuleRequest request)
    {
        var result = await _farePackageRulesService.UpdateAsync(id, request);
        return await result.Match<Task<IActionResult>>(async updated => updated switch
            {
                true => await GetByIdAsync(id),
                false => NotFound()
            },
            error => Task.FromResult<IActionResult>(error switch
            {
                ValidationError validation => ValidationProblem(new ValidationProblemDetails(validation.Errors)),
                DuplicateFoundError => Problem(error.Message, statusCode: StatusCodes.Status409Conflict),
                _ => Problem(error.Message, statusCode: StatusCodes.Status500InternalServerError)
            }));
    }

    [HttpDelete("{id:guid}")]
    [ApiAuthorize(AuthScopes.FarePackagesDelete)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(NotFoundResult), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(OkResult), StatusCodes.Status200OK)]
    public async Task<IActionResult> DeleteAsync([FromRoute] Guid id)
    {
        var result = await _farePackageRulesService.DeleteAsync(id);
        return result.Match<IActionResult>(updated => updated switch
            {
                true => Ok(),
                false => NotFound()
            },
            error => error switch
            {
                ValidationError validation => ValidationProblem(new ValidationProblemDetails(validation.Errors)),
                _ => Problem(error.Message, statusCode: StatusCodes.Status500InternalServerError)
            });
    }
}
