using CTeleport.Services.FareRules.Service.Contracts;
using FluentValidation;

namespace CTeleport.Services.FareRules.Service.Validation;

public class CreateFarePackageRuleRequestValidator : AbstractValidator<CreateFarePackageRuleRequest>
{
    public CreateFarePackageRuleRequestValidator()
    {
        RuleFor(x => x.AirlineIataCode)
            .AirlineIataCode();

        RuleFor(x => x.FarePackageName)
            .NotEmpty();
    }
}
