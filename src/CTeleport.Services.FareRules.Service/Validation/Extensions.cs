using FluentValidation;

namespace CTeleport.Services.FareRules.Service.Validation;

public static class Extensions
{
    public static IRuleBuilderOptions<T, string> AirlineIataCode<T>(this IRuleBuilderInitial<T, string> ruleBuilder)
    {
        return ruleBuilder
            .NotNull()
            .Matches("^[A-Z0-9]{2}$")
            .WithMessage("Airline IATA code must be 2 uppercase letters or digits");
    }
}
