using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using CTeleport.Common.Authorization.Services;
using ServiceStack;

using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Common.Redis;
using CTeleport.Messages;
using CTeleport.Messages.Commands.Enums;
using CTeleport.Messages.Commands.Tickets;
using CTeleport.Messages.Events.FareRules;
using CTeleport.FareRules.Shared.Models.Annotation;

using CTeleport.Services.FareRules.Service.Extensions;
using CTeleport.Services.FareRules.Service.Helpers;
using CTeleport.Services.FareRules.Service.Repositories;
using CTeleport.Services.FareRules.Service.Repositories.Models;
using CTeleport.Services.FareRules.Service.Services.Interfaces;
using Serilog;
using Subrule = CTeleport.FareRules.Shared.Models.Subrule;
using FareRulesUser = CTeleport.FareRules.Shared.Models.User;

namespace CTeleport.Services.FareRules.Service.Services;

public class FareRulesService : IFareRulesService
{
    private readonly IServiceContext _context;
    private readonly IFareRulesRepository _repository;

    private readonly IMapper _mapper;
    private readonly ILockFactory _lockFactory;
    private readonly IMessageDispatcher _dispatcher;
    private readonly ILogger _logger;

    public FareRulesService(
        IServiceContext context,
        IFareRulesRepository repository,
        IMapper mapper,
        ILockFactory lockFactory,
        IMessageDispatcher dispatcher,
        ILogger logger)
    {
        _context = context;
        _repository = repository;
        _mapper = mapper;
        _lockFactory = lockFactory;
        _dispatcher = dispatcher;
        _logger = logger;
    }

    public async Task<List<FareRule16Entity>> GetFareRules16Async(bool onlyShortVersion, string searchQuery)
    {
        if (string.IsNullOrWhiteSpace(searchQuery))
            return await _repository.GetAsync(onlyShortVersion);

        const int idLength = 32;
        var filterById = searchQuery.Length == idLength;

        if (filterById)
        {
            return new List<FareRule16Entity> { await GetFareRule16Async(searchQuery) };
        }

        // filter by normalized text
        var normalizedText = searchQuery.NormalizeForFareRule(withUpperCase: false);
        return await _repository.GetByFilterAsync(onlyShortVersion, byNormalizedText: normalizedText);
    }

    public async Task<FareRule16Entity> GetFareRule16Async(string id) => await _repository.GetAsync(id);

    public async Task<FareRule16Entity> UpdateFareRule16Async(string id, FareRule16Entity newRuleData, bool canValidate)
    {
        FareRule16Entity fareRule;

        using (GetUpdateLock(id))
        {
            fareRule = await _repository.GetAsync(id, true);

            if (fareRule == null)
            {
                throw new NotFoundException("Fare rule configuration is not defined");
            }

            AuthoriseValidationFlags(fareRule, newRuleData, canValidate);

            fareRule.HasRefundsMarkup = newRuleData.HasRefundsMarkup;
            fareRule.AllowRefunds = newRuleData.AllowRefunds;
            fareRule.IsValidatedForRefunds = newRuleData.IsValidatedForRefunds;

            fareRule.HasChangesMarkup = newRuleData.HasChangesMarkup;
            fareRule.AllowChanges = newRuleData.AllowChanges;
            fareRule.IsValidatedForChanges = newRuleData.IsValidatedForChanges;

            ResetValidationFlags(fareRule, newRuleData.Subrules, canValidate);

            fareRule.Subrules = newRuleData.Subrules;
            fareRule.Version = newRuleData.Version;

            fareRule.UpdatedAt = DateTime.UtcNow;
            fareRule.UpdatedBy = _mapper.Map<FareRulesUser>(_context.User);
                
            await _repository.UpdateAsync(fareRule);
        }

        if (fareRule.IsValidatedForRefunds == true)
        {
            await RefundByFareRuleAsync(id);
        }

        return fareRule;
    }

    private static void AuthoriseValidationFlags(FareRule16Entity fareRule, FareRule16Entity newRuleData, bool canValidate)
    {
        if (!fareRule.IsValidatedForRefunds && newRuleData.IsValidatedForRefunds || !fareRule.IsValidatedForChanges && newRuleData.IsValidatedForChanges)
        {
            if (!canValidate)
                throw new AuthorisationException("User is not authorised to validate fare rules");
        }
    }

    private static void ResetValidationFlags(FareRule16Entity fareRule, List<Subrule> updatedSubrules, bool canValidate)
    {
        // NOTE: if user initiated update is authorised to validate, we can keep validation flags.
        // Just exit.
        if (canValidate)
        {
            return;
        }

        // Check if common part was updated, reset both validation flag to False.
        if (fareRule.Subrules.CommonPropertiesUpdated(updatedSubrules))
        {
            fareRule.IsValidatedForChanges = false;
            fareRule.IsValidatedForRefunds = false;
            return;
        }

        if (fareRule.Subrules.RefundRulesUpdated(updatedSubrules))
        {
            fareRule.IsValidatedForRefunds = false;
        }

        if (fareRule.Subrules.ChangesRulesUpdate(updatedSubrules))
        {
            fareRule.IsValidatedForChanges = false;
        }
    }

    public async Task<IEnumerable<string>> GetFareRuleIdsWithAnnotationsAsync()
        => (await _repository.GetAllAnnotationsAsync()).Select(a => a.Id);

    public async Task<Annotation> GetFareRuleAnnotationAsync(string id)
        => await _repository.GetAnnotationAsync(id);

    public async Task UpsertFareRuleAnnotationAsync(Annotation annotation)
    {
        await _repository.UpsertAnnotationAsync(annotation);
        await _dispatcher.DispatchAsync(new AnnotationUpdated(annotation.Id));
    }

    private async Task CountFareRuleAsync(CountFareRuleState state, string platingCarrier, IList<string> texts)
    {
        async Task UpdateFareRuleAsync(string text)
        {
            var id = FareRulesHelper.GetFareRuleIdByText(text);

            using (GetUpdateLock(id))
            {
                var fareRule = await _repository.GetAsync(id, true);

                if (fareRule != null)
                {
                    switch (state)
                    {
                        case CountFareRuleState.Created:
                            fareRule.ReservationsCount++;
                            break;
                        case CountFareRuleState.Cancelled:
                            fareRule.CancellationsCount++;
                            break;
                        case CountFareRuleState.Changed:
                            fareRule.ChangesCount++;
                            break;
                    }

                    fareRule.LatestReservation = DateTime.UtcNow;
                    fareRule.PlatingCarriers = (fareRule.PlatingCarriers ?? new List<string>()).Append(platingCarrier).Distinct().ToList();

                    await _repository.UpdateAsync(fareRule);
                }
                else
                {
                    fareRule = new FareRule16Entity
                    {
                        Id = id,
                        ReservationsCount = 1,
                        CancellationsCount = state == CountFareRuleState.Cancelled ? 1 : 0,
                        ChangesCount = state == CountFareRuleState.Changed ? 1 : 0,
                        LatestReservation = DateTime.UtcNow,
                        PlatingCarriers = new List<string> { platingCarrier },
                        CreatedAt = DateTime.UtcNow,
                        Version = "1",
                        RawText = text,
                        NormalizedText = text.NormalizeForFareRule(),
                        AllowRefunds = true,
                        AllowChanges = true
                    };

                    await _repository.AddAsync(fareRule);
                }
            }
        }

        // TODO: Consider an opportunity of creating new command here and dispatch it to update farerules
        await Task.WhenAll(texts.Distinct().Map(UpdateFareRuleAsync));
    }

    public Task CountReservationCreatedAsync(string platingCarrier, List<string> texts) => CountFareRuleAsync(CountFareRuleState.Created, platingCarrier, texts);

    public Task CountReservationCancelledAsync(string platingCarrier, List<string> texts) => CountFareRuleAsync(CountFareRuleState.Cancelled, platingCarrier, texts);


        public async Task ResetRuleAndMarkupAsync(string id)
        {
            using (GetUpdateLock(id))
            {
                var fareRule = await _repository.GetAsync(id, true);

                if (fareRule == null)
                    return;

                fareRule.ResetMarkup();
                await _repository.UpdateAsync(fareRule);
                await _repository.RemoveAnnotationAsync(id);
            }
        }
        

    public Task CountReservationChangesAsync(string platingCarrier, List<string> texts) => CountFareRuleAsync(CountFareRuleState.Changed, platingCarrier, texts);

    public async Task<(string FareRuleId, bool IsAlreadyExist)> AddNewFareRule16Async(Subrule subrule, string rawText)
    {
        string id = FareRulesHelper.GetFareRuleIdByText(rawText);
        
        var fareRule = await _repository.GetAsync(id, true);
        if (fareRule != null)
        {
            return (fareRule.Id, true);
        }
        
        var dto = new FareRule16Entity
        {
            Id = id,
            LatestReservation = DateTime.UtcNow,
            RawText = rawText,
            NormalizedText = rawText.NormalizeForFareRule(),
            AllowRefunds = true,
            AllowChanges = true,
            Subrules = new List<Subrule> { subrule },
            Version = "2",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = new FareRulesUser { Email = _context.User.Email, Name = _context.User.Name }
        };
        
        await _repository.AddAsync(dto);

        return (id, false);
    }

    public async Task PersistFareRuleSectionsAsync(List<FareRuleSectionEntity> sections)
    {
        Func<FareRuleSectionEntity, bool> filterWhereIdExists = section => !string.IsNullOrEmpty(section.Id); 
        var sectionsWithIds = sections.Where(filterWhereIdExists).ToList();
        
        _logger.Information("Persisting {Count} fare rule sections out of {TotalCount}", sectionsWithIds.Count, sections.Count);
        
        if (sectionsWithIds.Count == 0)
            return;

        if (sectionsWithIds.Count != sections.Count)
        {
            _logger.Warning("Some fare rule sections do not have an ID on the persistence request.");
        }
        
        await _repository.InsertFareRulesSectionsIfNotExist(sectionsWithIds);
    }

    public async Task<List<FareRuleSectionEntity>> GetFareRuleSectionsByIdsAsync(List<string> ids)
    {
        return await _repository.GetFareRuleSectionsByIdsAsync(ids);
    }

    private IDisposable GetUpdateLock(string id) =>
        _lockFactory.AcquireLock($"FareRulesService.UpdateFareRule{id}", TimeSpan.FromMinutes(1));

    private async Task RefundByFareRuleAsync(string fareRuleId)
    {
        // TODO: Refactor how comman and conext is built. Move to CTeleport.Common/CTeleport.Messaging?

        var command = new RefundByFareRule
        {
            FareRuleCat16Id = fareRuleId,
            User = new CTeleport.Messages.Commands.Models.User
            {
                Id = _context.User.Id,
                Name = _context.User.Name,
                Email = _context.User.Email,
                TenantId = _context.TenantId
            },
            Request = Messages.Commands.Request.New<RefundByFareRule>()
        };

        var context = new MessageContext
        {
            User = new Messages.Models.User
            {
                Id = _context.User.Id,
                Name = _context.User.Name,
                Email = _context.User.Email,
                Roles = _context.User.Roles,
                TenantId = _context.TenantId
            }
        };

        await _dispatcher.DispatchAsync(command, context: context);
    }
}
