using System.Threading.Tasks;
using CTeleport.FareRules.Shared.Models.Annotation;
using CTeleport.Services.FareRules.Service.Repositories;
using CTeleport.Services.FareRules.Service.Services.Interfaces;
using CTeleport.Services.FareRules.Service.Types;

namespace CTeleport.Services.FareRules.Service.Services;

public class FarePackageRulesAnnotationService : IFarePackageRulesAnnotationService
{
    private readonly IFarePackageRuleAnnotationRepository _repository;

    public FarePackageRulesAnnotationService(IFarePackageRuleAnnotationRepository repository)
    {
        _repository = repository;
    }

    public Task<Result<Annotation>> GetAnnotationAsync(string id)
        => _repository.GetAnnotationAsync(id);

    public Task<Result<bool>> UpsertAnnotationAsync(Annotation annotation)
        => _repository.UpsertAnnotationAsync(annotation);
}
