using CTeleport.Services.FareRules.Service.Repositories.Data;
using MongoDB.Bson;
using MongoDB.Driver;

namespace CTeleport.Services.FareRules.Service.Extensions;

internal static class PaginationExtensions
{
    public static PipelineDefinition<TIn, Pagination<TOut>> AddPagination<TIn, TOut>(
        this PipelineDefinition<TIn, TOut> builder, int page, int size) =>
        builder.Facet(AggregateFacet.Create("Data",
                    new EmptyPipelineDefinition<TOut>()
                        .Skip((page - 1) * size)
                        .Limit(size)),
                AggregateFacet.Create("Count",
                    new EmptyPipelineDefinition<TOut>()
                        .Count()))
            .Project<TIn, AggregateFacetResults, Pagination<TOut>>(new BsonDocument {
                {nameof(Pagination<TOut>.Data), "$Data"},
                {nameof(Pagination<TOut>.Total), new BsonDocument {
                    {"$arrayElemAt", new BsonArray {"$Count.count", 0}}
                }},
                {nameof(Pagination<TOut>.Size), new BsonDocument {
                    {"$literal", size}
                }},
                {nameof(Pagination<TOut>.Page), new BsonDocument {
                    {"$literal", page}
                }}
            });
}
