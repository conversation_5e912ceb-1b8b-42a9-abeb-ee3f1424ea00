using System;
using CTeleport.FareRules.Shared.Dto;
using CTeleport.FareRules.Shared.Models;
using CTeleport.Services.FareRules.Service.Contracts;
using CTeleport.Services.FareRules.Service.Repositories.Models;

namespace CTeleport.Services.FareRules.Service.Mapping;

public static class FarePackageRulesMappers
{
    public static FarePackageRuleDto MapToDto(this FarePackageRuleEntity entity)
        => new()
        {
            Id = entity.Id,
            CreatedAt = entity.CreatedAt,
            UpdatedAt = entity.UpdatedAt,
            CreatedBy = entity.CreatedBy,
            UpdatedBy = entity.UpdatedBy,
            Subrules = entity.Subrules,
            RawText = entity.RawText,
            AirlineIataCode = entity.AirlineIataCode,
            FarePackageName = entity.FarePackageName,
            AllowChanges = entity.AllowChanges,
            AllowRefunds = entity.AllowRefunds,
            HasRefundsMarkup = entity.HasRefundsMarkup,
            HasChangesMarkup = entity.HasChangesMarkup,
            IsValidatedForChanges = entity.IsValidatedForChanges,
            IsValidatedForRefunds = entity.IsValidatedForRefunds,
            DisableTravelfusionFee = entity.DisableTravelfusionFee
        };

    public static FarePackageRuleEntity MapToEntity(this CreateFarePackageRuleRequest request, Func<User> getUser)
        => new()
        {
            CreatedAt = DateTime.UtcNow,
            CreatedBy = getUser(),
            Subrules = request.Subrules ?? [],
            RawText = request.RawText ?? string.Empty,
            AirlineIataCode = request.AirlineIataCode,
            FarePackageName = request.FarePackageName,
            AllowChanges = request.AllowChanges ?? false,
            AllowRefunds = request.AllowRefunds ?? false,
            HasRefundsMarkup = request.HasRefundsMarkup ?? false,
            HasChangesMarkup = request.HasChangesMarkup ?? false,
            IsValidatedForChanges = request.IsValidatedForChanges ?? false,
            IsValidatedForRefunds = request.IsValidatedForRefunds ?? false,
            DisableTravelfusionFee = request.DisableTravelfusionFee ?? false
        };
}
