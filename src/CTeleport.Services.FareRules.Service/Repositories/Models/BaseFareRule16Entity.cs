using System;

namespace CTeleport.Services.FareRules.Service.Repositories.Models;

public class BaseFareRule16Entity
{
    /// <summary>
    /// Fare rules id.
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// Version
    /// </summary>
    public string Version { get; set; }

    /// <summary>
    /// Reservations count
    /// </summary>
    public int ReservationsCount { get; set; }

    /// <summary>
    /// Cancellations count
    /// </summary>
    public int CancellationsCount { get; set; }

    /// <summary>
    /// Changes count
    /// </summary>
    public int ChangesCount { get; set; }

    /// <summary>
    /// Date and time when this configuration was created, UTC
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Date and time when this configuration was updated, UTC
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// User who created this configuration
    /// </summary>
    public string CreatedBy { get; set; }

    /// <summary>
    /// User who updated this configuration
    /// </summary>
    public string UpdatedBy { get; set; }

    /// <summary>
    ///  Flag indicating that markup for refunds was finished. Set by user.
    /// </summary>
    public bool HasRefundsMarkup { get; set; }

    /// <summary>
    ///  Flag indicating that markup for changes was finished. Set by user.
    /// </summary>
    public bool HasChangesMarkup { get; set; }

    /// <summary>
    ///  Refunds are allowed for this fare rule. Fare rule interpretation can be trusted to build
    ///  a cancellation timeline and to calculate refund fees for automatic refun execution.
    ///  Set by user.
    /// </summary>
    public bool AllowRefunds { get; set; }

    /// <summary>
    /// Changes are allowed for this fare rule. Fare rule interpretation can be trusted to build
    /// a change fee timeline and to a change fee and other features for automatic ticket reissue.
    /// Set by user.
    /// </summary>
    public bool AllowChanges { get; set; }

    /// <summary>
    /// Flag indicating that this fare rule is validated for refunds. Set by authorised user only.
    /// Can be reset when fare rule is updated.
    /// </summary>
    public bool IsValidatedForRefunds { get; set; }

    /// <summary>
    /// Flag indicating that this fare rule is validated for changes. Set by authorised user only.
    /// Can be reset when fare rule is updated.
    /// </summary>
    public bool IsValidatedForChanges { get; set; }
        
    /// <summary>
    /// Does the rule have similar rules for refunds?
    /// </summary>
    public bool HasSimilarValidatedRefundsRule { get; set; }
        
    /// <summary>
    /// Does the rule have similar rules for changes?
    /// </summary>
    public bool HasSimilarValidatedChangesRule { get; set; }
}