using System;
using System.Collections.Generic;
using System.Linq;
using CTeleport.FareRules.Shared.Models;

namespace CTeleport.Services.FareRules.Service.Repositories.Models;

public class FareRule16Entity
{
    /// <summary>
    ///     Id calculated as md5 of sanitized text category 16 (Penalties) fare rule
    /// </summary>
    public string Id { get; set; }
        
    /// <summary>
    ///     Raw fare rule text, plain text as given by airline.
    /// </summary>
    public string RawText { get; set; }

    /// <summary>
    ///     Normalized raw fare rule text (without markup, spaces and new line characters).
    /// </summary>
    /// <remark>
    ///     Can be `null` (not initialized) for old fare rules.
    /// </remark>
    public string NormalizedText { get; set; }
        
    /// <summary>
    ///     In most cases rule contains the only subrule, but there may be rules with several subrules for different conditions
    /// </summary>
    public List<Subrule> Subrules { get; set; }

    /// <summary>
    ///     Date and time when this configuration was created, UTC
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    ///     Date and time when this configuration was updated, UTC
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    ///     User who created this configuration
    /// </summary>
    public User CreatedBy { get; set; }

    /// <summary>
    ///     User who updated this configuration
    /// </summary>
    public User UpdatedBy { get; set; }

    /// <summary>
    ///     Protocol version at the moment of validation
    /// </summary>
    public string Version { get; set; }

    /// <summary>
    ///     Total count of created reservations with the such fare rule
    /// </summary>
    public int ReservationsCount { get; set; }

    /// <summary>
    ///     Total count of cancelled reservations with the such fare rule
    /// </summary>
    public int CancellationsCount { get; set; }

    /// <summary>
    ///     Total count of changed reservations with the such fare rule
    /// </summary>
    public int ChangesCount { get; set; }

    /// <summary>
    ///     Date and time when reservation referencing this fare rules was registered for the last time
    /// </summary>
    /// <remark>
    ///     Can be `null` (not initialized) for old fare rules
    /// </remark>
    public DateTime? LatestReservation { get; set; }

    /// <summary>
    ///     List of plating carriers from reservations that were referencing this fare rule
    /// </summary>
    /// <remark>
    ///     Can be `null` (not initialized) for old fare rules
    /// </remark>
    public List<string> PlatingCarriers { get; set; }

    /// <summary>
    ///     Flag indicating that markup for refunds was finished. Set by user.
    /// </summary>
    public bool HasRefundsMarkup { get; set; }

    /// <summary>
    ///     Flag indicating that markup for changes was finished. Set by user.
    /// </summary>
    public bool HasChangesMarkup { get; set; }

    /// <summary>
    ///     Refunds are allowed for this fare rule. Fare rule interpretation can be trusted to build
    ///     a cancellation timeline and to calculate refund fees for automatic refun execution.
    ///     Set by user.
    /// </summary>
    public bool AllowRefunds { get; set; }

    /// <summary>
    ///     Changes are allowed for this fare rule. Fare rule interpretation can be trusted to build
    ///     a change fee timeline and to a change fee and other features for automatic ticket reissue.
    ///     Set by user.
    /// </summary>
    public bool AllowChanges { get; set; }

    /// <summary>
    ///     Flag indicating that this fare rule is validated for refunds. Set by authorised user only.
    ///     Can be reset when fare rule is updated.
    /// </summary>
    public bool IsValidatedForRefunds { get; set; }

    /// <summary>
    ///     Flag indicating that this fare rule is validated for changes. Set by authorised user only.
    ///     Can be reset when fare rule is updated.
    /// </summary>
    public bool IsValidatedForChanges { get; set; }

    /// <summary>
    /// Does the rule have similar rules for refunds?
    /// </summary>
    public bool HasSimilarValidatedRefundsRule { get; set; }
        
    /// <summary>
    /// Does the rule have similar rules for changes?
    /// </summary>
    public bool HasSimilarValidatedChangesRule { get; set; }
        
    /// <summary>
    ///     Old flag indicating that markup for changes was finished. Set by user.
    /// </summary>
    [Obsolete]
    public bool ValidationFinished { get; set; }

    /// <summary>
    ///     Temporary field, means that this rules was mapped from the old one that wasn't allowed to be refunded automatically
    /// </summary>
    [Obsolete]
    public bool ShouldBeRevalidated { get; set; }

    /// <summary>
    ///     Temporary field, means that this rules was mapped from the old protocol and was validated previously.
    /// </summary>
    [Obsolete]
    public bool WasValidated { get; set; }

    /// <summary>
    ///     Old property indicating that this rule can be trusted
    /// </summary>
    [Obsolete]
    public bool IsTrusted => Version == "0" || (ValidationFinished && Version != "1");


    /// <summary>
    ///     Old property indicating that auto refunds are allowed for this rule
    /// </summary>
    [Obsolete]
    public bool IsAutoRefundAllowed => Subrules != null && Subrules.All(r => r.AllowAutoRefund == true);

    /// <summary>
    /// Resets the rule to be remarkuped
    /// </summary>
    /// <returns></returns>
    public void ResetMarkup()
    {
        IsValidatedForChanges = false;
        IsValidatedForRefunds = false;
        Subrules = new List<Subrule>();
        AllowRefunds = true;
        AllowChanges = true;
        HasRefundsMarkup = false;
        HasChangesMarkup = false;
    }
}
