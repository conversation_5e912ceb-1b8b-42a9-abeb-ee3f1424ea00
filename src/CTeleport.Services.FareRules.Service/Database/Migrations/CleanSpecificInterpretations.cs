using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using CTeleport.FareRules.Shared.Models;
using CTeleport.FareRules.Shared.Models.Annotation;
using CTeleport.Services.FareRules.Service.Database.Migrations.Interfaces;
using CTeleport.Services.FareRules.Service.Repositories.Models;
using MongoDB.Driver;

namespace CTeleport.Services.FareRules.Service.Database.Migrations;

public class CleanSpecificInterpretations : IInterpretationsMigration
{
    private static readonly HashSet<string> _ids = new()
    {
        "43fe5f7f85573cae6041352c10a33f6d",
        "6590ccf0bed7b6c99bb3c82824ff04a4",
        "e812beaccda85e00d7101d91ee1ba4c8",
        "a99268529ab62cdaf22a3c7eb133fe36",
        "abeec93b6c159129ee05c667a42357ff",
        "6f819ef4978a05736869ddc2f8be1b40",
        "177f893abd4737eee232cb7a484ebc68",
        "c2ea150623e0f05b72e51ecb18faf7a2",
        "fd33f3da3c8946ff225e7751dbfbaef6",
        "d50b29d4cdd021e304c597beb808244f",
        "b0d7bea5515a3a759d11399ffe998779",
        "41bbe4dcc88b74c167424c91aca038a5",
        "01a016b04e8105e4edc7ed2f8cf268c1",
        "7ffc3a7872706c4ca490862193b32855",
        "4d0278b550d2c59bd5253bf220cd132d",
        "a38f5f0e11c78e82e5d111a53bf931ca",
        "07ab4275edc8288956344c579dcc2720",
        "33605774f27292c90d4adbe7b194f0ac",
        "333c5d524b67c22ba6bc551623fb5708",
        "0e7b35e924c765ac62c382674280aee8",
        "1b270136e3ecf07e17bdc0361d64e1b8",
        "3991637fa6c84bd5565e2ca8e808c7f6",
        "b6f72fc442988abbcc42938b6876a58a",
        "f5113aa77ca180390452da2ae800e1f4",
    };
        
    public FilterDefinition<FareRule16Entity> Filter => Builders<FareRule16Entity>.Filter.Where(f => _ids.Contains(f.Id));

    public FareRule16Entity UpdateEntity(FareRule16Entity input)
    {
        var firstSubrule = input.Subrules.First();
        input.Subrules = new List<Subrule> { firstSubrule };
        firstSubrule.Cancellation = null;
        input.HasRefundsMarkup = false;
        input.IsValidatedForRefunds = false;
        input.AllowRefunds = true;
            
        return input;
    }

    public Expression<Func<FareRule16Entity, bool>> GetId(FareRule16Entity entity) => e => e.Id == entity.Id;
}