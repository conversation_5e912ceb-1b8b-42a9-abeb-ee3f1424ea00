using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using CTeleport.FareRules.Shared.Models.Annotation;
using CTeleport.Services.FareRules.Service.Database.Migrations.Interfaces;
using MongoDB.Driver;
using Newtonsoft.Json;

namespace CTeleport.Services.FareRules.Service.Database.Migrations;

public class AnnotationCleanerMigration : IAnnotationMigration
{
    private static readonly HashSet<string> ExactMatch = new()
    {
        "/subrules[0]/change/change_fee_tax",
        "/subrules[0]/change/change_limited_to_fare_basis",
        "/subrules[0]/change/change_limited_to_fare_families",
        "/subrules[0]/change/permission",
        "/subrules[0]/change/free_change",
        "/subrules[0]/change/upgrade_cabin_class_free",
        "/subrules[0]/change/collect_fee_as_amd"
    };
        
    private static readonly HashSet<string> PartiallyMatch = new()
    {
        "/subrules[0]/change/change_limited_to_fare_basis",
        "/subrules[0]/change/change_limited_to_fare_families",
        "/subrules[0]/change/permission[",
        "/subrules[0]/change/upgrade_cabin_class_free",
        "/subrules[0]/change/fee/",
        "/subrules[0]/change/free_change[",
        "/subrules[0]/noShow/",
    };
        
    private readonly FilterDefinition<Annotation> _tokensFilter = Builders<Annotation>.Filter.Where(f =>
        f.Tokens != null
        && f.Tokens.Any(e =>
            e.Category != null &&
            e.Category.UserEntry != null &&
            e.Category.UserEntry.Count > 0)
        == true);
        
    private readonly FilterDefinition<Annotation> _sentencesFilter = Builders<Annotation>.Filter.Where(f =>
        f.Sentences != null
        && f.Sentences.Any(e =>
            e.Category != null &&
            e.Category.UserEntry != null &&
            e.Category.UserEntry.Count > 0)
        == true);
        
    private readonly FilterDefinition<Annotation> _predictorsFilter = Builders<Annotation>.Filter.Where(f =>
        f.Sentences != null
        && f.Sentences.Any(e =>
            e.Category != null &&
            e.Category.Predicted != null &&
            e.Category.Predicted.Count > 0)
        == true);

    public FilterDefinition<Annotation> Filter =>
        Builders<Annotation>.Filter.Or(_tokensFilter, _sentencesFilter, _predictorsFilter);

    public Annotation UpdateEntity(Annotation input)
    {
        string incomingData = JsonConvert.SerializeObject(input);
        foreach (AnnotationToken token in input?.Tokens ?? new List<AnnotationToken>(0))
        {
            CleanUserEntries(token?.Category?.UserEntry);
        }
            
        foreach (AnnotationSentence sentence in input?.Sentences ?? new List<AnnotationSentence>(0))
        {
            CleanUserEntryCategories(sentence?.Category?.UserEntry);
            foreach (var predictedCategory in sentence?.Category?.Predicted?.ToList() ?? new List<PredictedCategory>(0))
            {
                if (ShouldBeDeleted(predictedCategory?.Category))
                {
                    sentence?.Category?.Predicted?.Remove(predictedCategory);
                }
            }
        }
        return JsonConvert.SerializeObject(input) == incomingData ? null : input;
    }

    private static void CleanUserEntries(IList<string> userEntries)
    {
        foreach (string userEntry in userEntries?.ToList() ?? new List<string>(0))
        {
            if (ShouldBeDeleted(userEntry))
            {
                userEntries?.Remove(userEntry);
            }
        }
    }

    private static bool ShouldBeDeleted(string category) =>
        ExactMatch.Any(e => category?.Equals(e, StringComparison.InvariantCultureIgnoreCase) == true) ||
        PartiallyMatch.Any(e => category?.Contains(e, StringComparison.InvariantCultureIgnoreCase) == true);

    private static void CleanUserEntryCategories(IList<UserEntryCategory> userEntries)
    {
        foreach (UserEntryCategory userEntry in userEntries?.ToList() ?? new List<UserEntryCategory>(0))
        {
            if (ShouldBeDeleted(userEntry?.Category))
            {
                userEntries?.Remove(userEntry);
            }
        }
    }

    public Expression<Func<Annotation, bool>> GetId(Annotation entity) => e => e.Id == entity.Id;
}