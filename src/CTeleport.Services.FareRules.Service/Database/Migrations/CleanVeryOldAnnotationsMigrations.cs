using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using CTeleport.FareRules.Shared.Models.Annotation;
using CTeleport.Services.FareRules.Service.Database.Migrations.Interfaces;
using MongoDB.Driver;
using Newtonsoft.Json;

namespace CTeleport.Services.FareRules.Service.Database.Migrations;

public class CleanVeryOldAnnotationsMigrations : IAnnotationMigration
{
    private static readonly HashSet<string> _ids = new()
    {
        "3557cb6cbbd459c9c9e57858f1c23da9",
        "2f029c24377dac819870e3ecd347fe94",
        "5b2d00708fdfecf6a468f0f0bfd53326",
        "3e712e0d248db5b7139dda676d841f60",
        "97ea4f8d5631fe530774f46e40e1665a",
        "b3c1c45ba5415d473743eb519b650563",
        "4fd2bbcc66244527bdbe193d2c297ac5",
        "8bdb4d4228556b8c83f37e7fbf68356d",
        "e56d80ac29f5583a8b695ae98adc9f59",
        "495f766155ebb4402a84d62bd803a8ed",
        "4350db6fb1f31e30b518defd3700f362",
        "d94499d09f6e307d38df41a31491f98e",
        "7938e041ba8f1324773e1c15080a5ec9",
        "b701e1bb5bf1e829daf1ad5d156bcaea",
        "e9befd0f6bb777aaa06b3db34b462627",
        "cdbbf85526698d723a15490fde161063",
        "88e3fa6ca6b7855b9f16f20b502469ee",
        "95f524ef1a566053fc5496025f12f6b4",
        "dcfd8bf2631bf9ff6655f8364e200f78",
        "e712f4809edac98b75ef727a0c323d82",
        "868b23b2c338b8be7c2d09d0dfc0f48c",
        "0a8142766321c3caae934eaed7304517",
        "eefe781d5eac39be64eca61563971d86",
        "9dec4024c4316c80e3acd85760a54f09",
        "176ccfd42b81bad3484f0ace1753609c",
        "d337014c18c7297b035ab234f222a78f",
        "2bc5e5caa8c43244a884076b4b5ec168",
        "a9143cb68cc5caf9fd812eb268090a95",
        "30159e014086e27cde87f96eb6c198b9",
        "48c7d681b7ecbc0a364393e81904feb6",
        "c7a28ff32351b9311ea0fcc7f259da3f",
        "7633afe645dd5a49fa02007347854dea",
        "584f696ffc9d2d66f0920dd6054d0e76",
        "e106f69a0ce74b8f76e43b79c6d37966",
        "5b3d616bb286e69fc85c19d35bee5a6d",
        "beb4d296055e8b1635c3d3ef8c270ac2",
        "b7f0d41a5dd18f9c5ec4269016740425",
        "a34228491492b4359fce1ecee5175b60",
        "313f2dd1c51020a01b7a5332d0f40a8d",
        "bd59fb033398588cbe5095c16eaa34fa",
        "1cdc41ece02f3ad96f54ce89a0c1f852",
        "4301ac29d7a9bdbaf99194a2b3cf0787",
        "7eb34702aee97bf36491cca2c60011bb",
        "9963b8c60b3067ddd6d45a0cb4022ed8",
        "68728f793bcc586e5e3f940fe7a1d6ab",
        "5664f4c247ae751bc49f8ef162e1e0cd",
        "370201ece84a3682e44c06458d81c588",
        "0cbf5b49920ae902ef04f3751bf63cdd",
        "1b3892636ffdd938eedded4ab43d1665",
        "144af226868641752bcb1bb0baa981c3",
        "fc80420cff93c755b4743153555bb872",
        "a026408839a9a63b44cd0deba274ad08",
        "933231921562059d316252041575e47a",
        "842f981dcad18b97b451b6d2cc82b5b1",
        "ec29c7d0149d38eba0ad8f3b5b210ca3",
        "c527d4a6f3903a62846918095bae65b1",
        "90f7e949c16d0be91f67b71c39cb2c87",
        "30b19ad08b6457bf5e3a03b77b49dfe3",
        "e54e25077465bc939df3e70ff003f47c",
        "c8148fa0661974c02384825177cbbb2a",
        "5ad4d5a9509692cadaf4fb57af786ec8",
        "42976ef298ed3d89f390f549c30993cf",
        "2648daf1055cc85d365d29ca044aac3b",
        "233188eefa6330f67755277eda504497",
        "46697843a18cc50d18eb40f1b4fbd30e",
        "b23a07a045fd6b55cf79e3b450b9930b",
        "5676f3eb265bd4569eb25ed2288d95fc",
        "e22ac41331ee67cd12bd6f0514d5412c",
        "136e3986d03a19169df9140f1c0a6a35",
        "ad4862bc4e6758db54c66507357b1523",
        "70f30c920bd70f9892aed805982e7605",
        "6f47e13a53b5392f9c8437f1e57d75c9",
        "7af4ec1fec5ed224e49e8a45121b0c3f",
        "4d6d71f665d0ac6b0b0280ed7b33e6ca",
        "48470c9d6405799d44276a5cd7ba6182",
        "e9dd0c3adb0bd78af3d1d2c97df0a45b",
        "dfdd5afb1f61aaeb37fc13cfa9ff800a",
        "556615f2f236fde98769feddfb25b090",
        "666ba5858e5c86fb41d369599058c185",
        "12b1b2a8927941217ae44b2d79db432a",
        "19ddef43ba54a20fc47f3cd9e5a09493",
        "e35618ec64ace6e12c9afc0c85323229",
        "4e66a3cb272ebf61d13ae26b6c8cd0cb",
        "56b29b4f23fdc46fbeb5d006db7e58be",
        "2c3146c7ac19f64163de87536ea9e413",
        "8a3a031d38ae3b85efc58c52f53c0c3b",
        "e34f094f2b8cd6c60fdbecbcd0667f2d",
        "81f81ac26d72434e286542903a4dcbdc",
        "37474749d5af37aa4051bc331c2ec9aa",
        "cc099638c2614b337572c0ff4761c3d1",
        "c859927de800a57a2ede2ebdf94a01fb",
        "78d4c4ec49cfe5842b0e0a3709ac0c05",
        "6b8dbca944bd7268bd5e540d80f2c34c",
        "009cd30149f899b3f873c686f64dd98b"
    };
        
    private static readonly HashSet<string> ExactMatch = new()
    {
        "subrules[0].no_show.change_definition",
        "subrules[0]/no_show.change_definition",
        "subrules[0]/no_show/change_definition",
        "/subrules[0].no_show.change_definition",
        "/subrules[0]/no_show.change_definition",
        "/subrules[0]/no_show/change_definition"
    };
        
    private static readonly HashSet<string> PartiallyMatch = new()
    {
        "subrules[0].change",
        "subrules[0]/change"
    };

        
    public FilterDefinition<Annotation> Filter => Builders<Annotation>.Filter.Where(f => _ids.Contains(f.Id));

    public Annotation UpdateEntity(Annotation input)
    {
        string incomingData = JsonConvert.SerializeObject(input);
        foreach (AnnotationToken token in input?.Tokens ?? new List<AnnotationToken>(0))
        {
            CleanUserEntries(token?.Category?.UserEntry);
        }
            
        foreach (AnnotationSentence sentence in input?.Sentences ?? new List<AnnotationSentence>(0))
        {
            CleanUserEntryCategories(sentence?.Category?.UserEntry);
            foreach (var predictedCategory in sentence?.Category?.Predicted?.ToList() ?? new List<PredictedCategory>(0))
            {
                if (ShouldBeDeleted(predictedCategory?.Category))
                {
                    sentence?.Category?.Predicted?.Remove(predictedCategory);
                }
            }
        }
        return JsonConvert.SerializeObject(input) == incomingData ? null : input;
    }

    private static void CleanUserEntries(IList<string> userEntries)
    {
        foreach (string userEntry in userEntries?.ToList() ?? new List<string>(0))
        {
            if (ShouldBeDeleted(userEntry))
            {
                userEntries?.Remove(userEntry);
            }
        }
    }

    private static bool ShouldBeDeleted(string category) =>
        ExactMatch.Any(e => category?.Equals(e, StringComparison.InvariantCultureIgnoreCase) == true) ||
        PartiallyMatch.Any(e => category?.Contains(e, StringComparison.InvariantCultureIgnoreCase) == true);

    private static void CleanUserEntryCategories(IList<UserEntryCategory> userEntries)
    {
        foreach (UserEntryCategory userEntry in userEntries?.ToList() ?? new List<UserEntryCategory>(0))
        {
            if (ShouldBeDeleted(userEntry?.Category))
            {
                userEntries?.Remove(userEntry);
            }
        }
    }

    public Expression<Func<Annotation, bool>> GetId(Annotation entity) => e => e.Id == entity.Id;
}