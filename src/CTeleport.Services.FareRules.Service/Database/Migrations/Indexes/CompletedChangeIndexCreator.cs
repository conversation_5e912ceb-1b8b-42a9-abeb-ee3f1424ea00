using System.Threading.Tasks;
using CTeleport.Services.FareRules.Service.Database.Migrations.Interfaces;
using CTeleport.Services.FareRules.Service.Repositories.Models;
using CTeleport.Services.FareRules.Service.Repositories.Queries;
using MongoDB.Driver;

namespace CTeleport.Services.FareRules.Service.Database.Migrations.Indexes;

public class CompletedChangeIndexCreator : IIndexesCreator
{
    private readonly IMongoDatabase _database;

    public CompletedChangeIndexCreator(IMongoDatabase database)
    {
        _database = database;
    }

    public async Task CreateIfNeededAsync()
    {
        var index = Builders<FareRule16Entity>.IndexKeys
            .Ascending(s => s.IsValidatedForChanges)
            .Ascending(s => s.HasChangesMarkup)
            .Ascending(s => s.AllowChanges);

        var model = new CreateIndexModel<FareRule16Entity>(index);

        await _database.FareRules16New().Indexes.CreateOneAsync(model);
    }
}