using System;
using System.Linq.Expressions;
using CTeleport.FareRules.Shared.Models;
using CTeleport.Services.FareRules.Service.Database.Migrations.Interfaces;
using CTeleport.Services.FareRules.Service.Repositories.Models;
using MongoDB.Driver;

namespace CTeleport.Services.FareRules.Service.Database.Migrations;

public class ValidationPropertiesMigration : IInterpretationsMigration
{
    public FilterDefinition<FareRule16Entity> Filter => 
        Builders<FareRule16Entity>.Filter.Exists(f => f.IsValidatedForRefunds, exists: false);

    public FareRule16Entity UpdateEntity(FareRule16Entity input)
    {
        switch (input.Version)
        {
            case "0":
                input.IsValidatedForRefunds = input.ValidationFinished;
                input.HasRefundsMarkup = false;
                input.AllowRefunds = input.IsAutoRefundAllowed;
                input.AllowChanges = input.IsAutoRefundAllowed;
                return input;
            case "1":
                input.IsValidatedForRefunds = false;
                input.HasRefundsMarkup = false;
                input.AllowRefunds = true;
                input.AllowChanges = true;
                return input;
            case "2":
                input.IsValidatedForRefunds = input.ValidationFinished;
                input.HasRefundsMarkup = true;
                input.AllowRefunds = input.IsAutoRefundAllowed;
                input.AllowChanges = input.IsAutoRefundAllowed;
                return input;
            default:
                return input;
        }
    }
        
    public Expression<Func<FareRule16Entity, bool>> GetId(FareRule16Entity entity) => e => e.Id == entity.Id;
}
