using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Common.Redis;
using CTeleport.FareRules.Shared.Models.Annotation;
using CTeleport.Services.FareRules.Service.Database.Migrations.Interfaces;
using CTeleport.Services.FareRules.Service.Repositories.Models;
using CTeleport.Services.FareRules.Service.Repositories.Queries;
using Microsoft.AspNetCore.Hosting;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;
using ILogger = Serilog.ILogger;

namespace CTeleport.Services.FareRules.Service.Database;

public class DatabaseSeeder : IDatabaseSeeder
{
    private readonly IMongoDatabase _database;
    private readonly IInterpretationsMigration[] _migrations;
    private readonly IAnnotationMigration[] _annotationMigrations;
    private readonly IIndexesCreator[] _indexesCreators; 
    private readonly ILogger _logger;
    private readonly ILockFactory _lockFactory;
    private readonly IHostingEnvironment _env;

    private const string SemaphoreKey = "Semaphore/FareRulesService_DatabaseSeeder";

    public DatabaseSeeder(IMongoDatabase database, IInterpretationsMigration[] migrations, ILogger logger, ILockFactory lockFactory, IHostingEnvironment env, IAnnotationMigration[] annotationMigrations, IIndexesCreator[] indexesCreators)
    {
        _database = database;
        _migrations = migrations;
        _logger = logger;
        _lockFactory = lockFactory;
        _env = env;
        _annotationMigrations = annotationMigrations;
        _indexesCreators = indexesCreators;
    }

    public async Task SeedAsync()
    {
        using (_lockFactory.AcquireLock(SemaphoreKey, TimeSpan.FromMinutes(5)))
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            _logger.Information("Running CTeleport.Services.FareRules.DatabaseSeeder");

            foreach (var migration in _migrations  ?? Array.Empty<IInterpretationsMigration>())
            {
                await MigrateAsync(migration);
            }

            foreach (var migration in _annotationMigrations  ?? Array.Empty<IAnnotationMigration>())
            {
                await MigrateAsync(migration);
            }
                
            foreach (var indexesCreator in _indexesCreators ?? Array.Empty<IIndexesCreator>())
            {
                await indexesCreator.CreateIfNeededAsync();
            }
                
            if (_env.IsStaging() || _env.IsDevelopment())
            {
                // NOTE: to support integration tests
                await InsertTestFareRulesAsync();
            }

            _logger.Information($"CTeleport.Services.FareRules.DatabaseSeeder was executed successfully in {stopwatch.Elapsed:g}");

        }
    }

    private async Task MigrateAsync<T>(IMigration<T> migration) where T : class
    {
        string migrationName = migration.GetType().Name;

        _logger.Information($"Start {migrationName}");

        try
        {
            var collection = GetCollection<T>();

            long totalCount = await collection.CountDocumentsAsync(migration.Filter);

            const int batchSize = 10;

            bool isAnyUpdate = false;

            var options = new FindOptions<T>
            {
                BatchSize = batchSize
            };

            using var cursor = await collection.FindAsync(migration.Filter, options);
            int counter = 0;
            while (await cursor.MoveNextAsync())
            {
                var entities = cursor.Current.ToList();

                foreach (T entity in entities)
                {
                    var updated = migration.UpdateEntity(entity);
                    if (updated != null)
                    {
                        isAnyUpdate = true;
                        await collection.ReplaceOneAsync(migration.GetId(entity), entity);
                    }

                    _logger.Information("{Counter}/{Count} fare rules have been migrated", ++counter, totalCount);
                }
            }

            if (!isAnyUpdate)
                _logger.Information("No data for migration");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, $"Error in {migrationName}");
        }
        finally
        {
            _logger.Information($"End {migrationName}");
        }
    }

    private IMongoCollection<T> GetCollection<T>() where T : class
    {
        return typeof(T).Name switch
        {
            nameof(FareRule16Entity) => (IMongoCollection<T>)_database.FareRules16New(true),
            nameof(Annotation) => (IMongoCollection<T>)_database.Annotation(),
            _ => throw new Exception($"Invalid entity type in migrations: {typeof(T).Name}")
        };
    }

    private IDictionary<string, string> FareRules = new Dictionary<string, string>
    {
        ["f25805e4cd1865f616a2e1c4a2689764"] = @"{
    '_id' : 'f25805e4cd1865f616a2e1c4a2689764',
    'RawText' : 'PENALTIES\nUNLESS OTHERWISE SPECIFIED\nCANCELLATIONS\nANY TIME\nCANCELLATIONS PERMITTED FOR CANCEL/REFUND.\nNOTE -\nWHEN COMBINING ON A HALF ROUNDTRIP BASIS THE\nPENALTY CONDITIONS FOR EACH FARE COMPONENT APPLY.\n--------------------------------------------------\nFOR PARTIALLY USED TICKET -\nREFUND THE DIFFERENCE BETWEEN THE FARE PAID AND\nAPPLICABLE - EQUAL OR HIGHER - BT ONEWAY FARE\nFOR THE SECTOR FLOWN.\n--------------------------------------------------\nWHEN COMBINING NON-REFUNDABLE FARES WITH\nREFUNDABLE FARES\n- THE AMOUNT PAID ON EACH REFUNDABLE FARE\nCOMPONENT IS REFUNDED\n- THE AMOUNT PAID ON EACH NON-REFUNDABLE FARE\nCOMPONENT WILL NOT BE REFUNDED\n- WHEN COMBINING FARES CHARGE THE SUM OF THE\nCANCELLATION FEES OF ALL CANCELLED FARE COMPONENTS\n--------------------------------------------------\nIF OB FEE IS USED AS PART OF FORM OF PAYMENT THEN\nALSO OB FEES ARE REFUNDABLE.\n--------------------------------------------------\nREFUND OF UNUSED TAXES PERMITTED FOR FULLY UNUSED\nFARE COMPONENT.IF PART OF FARE COMPONENT IS USED -\nIN THIS CASE NO FARE/TAX REFUND WILL BE PERMITTED.\nCHANGES\nANY TIME\nCHANGES PERMITTED.\nNOTE -\nUPGRADE -\nPERMITTED WITHIN BUSINESS CLASS COMPARTMENT TO\nNEXT AVAILABLE BOOKING CLASS BY CHARGING\nDIFFERENCE BETWEEN THE CLASSES PER FARE COMPONENT\n--------------------\nANY TIME\nIN CASE OF TICKET UPGRADE AND REFUND THE ORIGINAL\nNONREFUNDABLE AMOUNT REMAINS NONREFUNDABLE.\n--------------------\nREROUTING IS NOT PERMITTED\n-------------------------------------------------\nNAME CHANGE PERMITTED. PLEASE CONTACT BT FOR MORE\nDETAILS.\n-------------------------------------------------\nIF THE FLIGHT SEGMENT FOR OUTBOUND TRAVEL FROM\nFARE COMPONENT IS UNUSED THE FLIGHT SEGMENT FOR\nINBOUND TRAVEL COULD BE USED FOR FLIGHTS/DATES\nAS PER PURCHASED TICKET/ELECTRONIC TICKET',
    'NormalizedText' : 'PENALTIESUNLESSOTHERWISESPECIFIEDCANCELLATIONSANYTIMECANCELLATIONSPERMITTEDFORCANCEL/REFUND.NOTE-WHENCOMBININGONAHALFROUNDTRIPBASISTHEPENALTYCONDITIONSFOREACHFARECOMPONENTAPPLY.--------------------------------------------------FORPARTIALLYUSEDTICKET-REFUNDTHEDIFFERENCEBETWEENTHEFAREPAIDANDAPPLICABLE-EQUALORHIGHER-BTONEWAYFAREFORTHESECTORFLOWN.--------------------------------------------------WHENCOMBININGNON-REFUNDABLEFARESWITHREFUNDABLEFARES-THEAMOUNTPAIDONEACHREFUNDABLEFARECOMPONENTISREFUNDED-THEAMOUNTPAIDONEACHNON-REFUNDABLEFARECOMPONENTWILLNOTBEREFUNDED-WHENCOMBININGFARESCHARGETHESUMOFTHECANCELLATIONFEESOFALLCANCELLEDFARECOMPONENTS--------------------------------------------------IFOBFEEISUSEDASPARTOFFORMOFPAYMENTTHENALSOOBFEESAREREFUNDABLE.--------------------------------------------------REFUNDOFUNUSEDTAXESPERMITTEDFORFULLYUNUSEDFARECOMPONENT.IFPARTOFFARECOMPONENTISUSED-INTHISCASENOFARE/TAXREFUNDWILLBEPERMITTED.CHANGESANYTIMECHANGESPERMITTED.NOTE-UPGRADE-PERMITTEDWITHINBUSINESSCLASSCOMPARTMENTTONEXTAVAILABLEBOOKINGCLASSBYCHARGINGDIFFERENCEBETWEENTHECLASSESPERFARECOMPONENT--------------------ANYTIMEINCASEOFTICKETUPGRADEANDREFUNDTHEORIGINALNONREFUNDABLEAMOUNTREMAINSNONREFUNDABLE.--------------------REROUTINGISNOTPERMITTED-------------------------------------------------NAMECHANGEPERMITTED.PLEASECONTACTBTFORMOREDETAILS.-------------------------------------------------IFTHEFLIGHTSEGMENTFOROUTBOUNDTRAVELFROMFARECOMPONENTISUNUSEDTHEFLIGHTSEGMENTFORINBOUNDTRAVELCOULDBEUSEDFORFLIGHTS/DATESASPERPURCHASEDTICKET/ELECTRONICTICKET',
    'Subrules' : [ 
        {
            'Change' : null,
            'Cancellation' : null
        }
    ],
    'CreatedAt' : ISODate('2020-01-29T14:15:26.448Z'),
    'UpdatedAt' : ISODate('2020-04-01T14:52:51.853Z'),
    'UpdatedBy' : {
        '_id' : 'auth0|5919670e6e197c4a0c44bd5f',
        'Name' : 'Rost',
        'Email' : '<EMAIL>',
        'Roles' : [ 
            'system'
        ]
    },
    'Version' : '2',
    'ReservationsCount' : 241,
    'CancellationsCount' : 227,
    'ChangesCount' : 0,
    'AllowRefunds' : true,
    'AllowChanges' : true,
    'IsValidatedForRefunds' : true,
    'IsValidatedForChanges' : true
}",
        ["8db9e4b1b105ed61f5119c993881fca4"] = @"{
    '_id' : '8db9e4b1b105ed61f5119c993881fca4',
    'RawText' : 'PE.PENALTIES\nFOR ECONOMY RESTRICTED FARES\n \n  FOR RESERVATIONS ON/AFTER 10MAR 20 AND ON/BEFORE 31MAR 20/\n  FOR TICKETING ON/AFTER 10MAR 20 AND ON/BEFORE 31MAR 20/FOR\n  TRAVEL ON/BEFORE 31DEC 20\n    CHANGES\n \n      BEFORE DEPARTURE\n        CHANGES PERMITTED.\n         NOTE -\n          ONLY ONE CHANGE PERMITTED FREE OF CHARGE\n          OUT OF THIS CAMPAIGN REGULAR PENALTY APPLIES\n          --------------------------------------------------\n          UPGRADE - PERMITTED TO HIGHER - PRM TYPE FARE\n          BY CHARGING THE DIFFERENCE BETWEEN CLASSES PER\n          FARE COMPONENT\n          --------------------------------------------------\n          REROUTING IS NOT PERMITTED\n          --------------------------------------------------\n          NAME CHANGE PERMITTED AT FEE 70EUR PER TICKET\n          PLUS FARE DIFFERENCE BETWEEN CLASSES.\n \n  CANCELLATIONS\n \n    ANY TIME\n      CHARGE EUR 50.00 FOR CANCEL/REFUND.\n         NOTE -\n          ANY TIME CHARGE 50.00 EUR FOR CANCEL/REFUND\n          PER FARE COMPONENT. CHARGE DOES NOT APPLY TO\n          INFANTS PAYING 10PCT OF THE ADULT FARE\n          --------------------------------------------------\n          IN CASE OF NO-SHOW FARE COMPONENT IS NON-\n          REFUNDABLE\n          --------------------------------------------------\n          PENALTY FEE SHOULD BE CALCULATED FROM TOTAL\n          AMOUNT - APPLICABLE OW/RT FARE PLUS YQ TAX FOR\n          ACTUAL TRAVEL. IF TICKET FARE AND YQ LESS THAN\n          CANCELLATION FEE - ONLY AIRPORT TAX REFUND\n          POSSIBLE\n          --------------------------------------------------\n          FOR PARTIALLY USED TICKET -\n          REFUND THE DIFFERENCE BETWEEN THE FARE PAID AND\n          APPLICABLE - EQUAL OR HIGHER - BT ONEWAY FARE\n          FOR THE SECTOR FLOWN\n          --------------------------------------------------\n          WHEN COMBINING NON-REFUNDABLE FARES WITH\n          REFUNDABLE FARES\n          - THE AMOUNT PAID ON EACH REFUNDABLE FARE\n          COMPONENT IS REFUNDED\n          - THE AMOUNT PAID ON EACH NON-REFUNDABLE FARE\n          COMPONENT WILL NOT BE REFUNDED\n          - WHEN COMBINING FARES CHARGE THE SUM OF THE\n          CANCELLATION FEES OF ALL CANCELLED FARE COMPONENTS\n          --------------------------------------------------\n          IF OB FEE IS USED AS PART OF FORM OF PAYMENT THEN\n          ALSO OB FEES ARE NON-REFUNDABLE\n          --------------------------------------------------\n          REFUND OF UNUSED TAXES PERMITTED FOR FULLY UNUSED\n          FARE COMPONENT.IF PART OF FARE COMPONENT IS USED -\n          IN THIS CASE NO FARE/TAX REFUND WILL BE PERMITTED.\n \n  FOR RESERVATIONS ON/AFTER 01APR 20/FOR TICKETING ON/AFTER\n  01APR 20\n    CHANGES\n \n      BEFORE DEPARTURE\n        CHARGE EUR 70.00.\n         NOTE -\n          CHARGE EUR70.00 FOR REVALIDATION PER\n          FARE COMPONENT.CHARGE DOES NOT APPLY TO INFANTS\n          PAYING 10PCT OF THE ADULT FARE\n          --------------------------------------------------\n          WHEN COMBINING ON A HALF ROUNDTRIP BASIS THE\n          PENALTY CONDITIONS FOR EACH FARE COMPONENT APPLY\n          --------------------------------------------------\n          REISSUE AND PAYMENT FOR CHANGE\n          MUST BE COMPLETED WITHIN 24HOURS AFTER RESERVATION\n          CHANGE HAS BEEN MADE  BUT  NOT LATER THAN PRIOR TO\n          DEPARTURE OF EITHER THE FLIGHT BEING CHANGED OR\n          THE NEW FLIGHT WHICHEVER IS EARLIER.\n          --------------------------------------------------\n          UPGRADE-\n          PERMITTED TO HIGHER -PRM TYPE FARE\n          OR/AND BUSINESS COMPARTMENT BY CHARGING PENALTY\n          OF EUR 70 AND THE DIFFERENCE BETWEEN\n          CLASSES PER FARE COMPONENT.\n          --------------------------------------------------\n          FOR AUTOMATIC/MANUAL REISSUE PENALTY FEE MUST BE\n          COLLECTED ON AN EMD AND MUST REMAIN NON\n          REFUNDABLE.\n          --------------------------------------------------\n          ANY TIME\n          IN CASE OF TICKET UPGRADE AND REFUND THE ORIGINAL\n          NONREFUNDABLE AMOUNT REMAINS NONREFUNDABLE.\n          --------------------------------------------------\n          REROUTING IS NOT PERMITTED\n          --------------------------------------------------\n          NAME CHANGE PERMITTED AT FEE 70EUR PER TICKET\n          PLUS FARE DIFFERENCE BETWEEN CLASSES.\n \n  CHANGES\n \n    AFTER DEPARTURE\n      CHANGES NOT PERMITTED.\n         NOTE -\n          WHEN COMBINING ON A HALF ROUNDTRIP BASIS THE\n          PENALTY CONDITIONS FOR EACH FARE COMPONENT APPLY\n          ------------------------------------------------\n          IF THE FLIGHT SEGMENT FOR OUTBOUND TRAVEL FROM\n          FARE COMPONENT IS UNUSED THE FLIGHT SEGMENT FOR\n          INBOUND TRAVEL COULD BE USED FOR FLIGHTS/DATES\n          AS PER PURCHASED TICKET/ELECTRONIC TICKET',
    'NormalizedText' : 'PE.PENALTIESFORECONOMYRESTRICTEDFARESFORRESERVATIONSON/AFTER10MAR20ANDON/BEFORE31MAR20/FORTICKETINGON/AFTER10MAR20ANDON/BEFORE31MAR20/FORTRAVELON/BEFORE31DEC20CHANGESBEFOREDEPARTURECHANGESPERMITTED.NOTE-ONLYONECHANGEPERMITTEDFREEOFCHARGEOUTOFTHISCAMPAIGNREGULARPENALTYAPPLIES--------------------------------------------------UPGRADE-PERMITTEDTOHIGHER-PRMTYPEFAREBYCHARGINGTHEDIFFERENCEBETWEENCLASSESPERFARECOMPONENT--------------------------------------------------REROUTINGISNOTPERMITTED--------------------------------------------------NAMECHANGEPERMITTEDATFEE70EURPERTICKETPLUSFAREDIFFERENCEBETWEENCLASSES.CANCELLATIONSANYTIMECHARGEEUR50.00FORCANCEL/REFUND.NOTE-ANYTIMECHARGE50.00EURFORCANCEL/REFUNDPERFARECOMPONENT.CHARGEDOESNOTAPPLYTOINFANTSPAYING10PCTOFTHEADULTFARE--------------------------------------------------INCASEOFNO-SHOWFARECOMPONENTISNON-REFUNDABLE--------------------------------------------------PENALTYFEESHOULDBECALCULATEDFROMTOTALAMOUNT-APPLICABLEOW/RTFAREPLUSYQTAXFORACTUALTRAVEL.IFTICKETFAREANDYQLESSTHANCANCELLATIONFEE-ONLYAIRPORTTAXREFUNDPOSSIBLE--------------------------------------------------FORPARTIALLYUSEDTICKET-REFUNDTHEDIFFERENCEBETWEENTHEFAREPAIDANDAPPLICABLE-EQUALORHIGHER-BTONEWAYFAREFORTHESECTORFLOWN--------------------------------------------------WHENCOMBININGNON-REFUNDABLEFARESWITHREFUNDABLEFARES-THEAMOUNTPAIDONEACHREFUNDABLEFARECOMPONENTISREFUNDED-THEAMOUNTPAIDONEACHNON-REFUNDABLEFARECOMPONENTWILLNOTBEREFUNDED-WHENCOMBININGFARESCHARGETHESUMOFTHECANCELLATIONFEESOFALLCANCELLEDFARECOMPONENTS--------------------------------------------------IFOBFEEISUSEDASPARTOFFORMOFPAYMENTTHENALSOOBFEESARENON-REFUNDABLE--------------------------------------------------REFUNDOFUNUSEDTAXESPERMITTEDFORFULLYUNUSEDFARECOMPONENT.IFPARTOFFARECOMPONENTISUSED-INTHISCASENOFARE/TAXREFUNDWILLBEPERMITTED.FORRESERVATIONSON/AFTER01APR20/FORTICKETINGON/AFTER01APR20CHANGESBEFOREDEPARTURECHARGEEUR70.00.NOTE-CHARGEEUR70.00FORREVALIDATIONPERFARECOMPONENT.CHARGEDOESNOTAPPLYTOINFANTSPAYING10PCTOFTHEADULTFARE--------------------------------------------------WHENCOMBININGONAHALFROUNDTRIPBASISTHEPENALTYCONDITIONSFOREACHFARECOMPONENTAPPLY--------------------------------------------------REISSUEANDPAYMENTFORCHANGEMUSTBECOMPLETEDWITHIN24HOURSAFTERRESERVATIONCHANGEHASBEENMADEBUTNOTLATERTHANPRIORTODEPARTUREOFEITHERTHEFLIGHTBEINGCHANGEDORTHENEWFLIGHTWHICHEVERISEARLIER.--------------------------------------------------UPGRADE-PERMITTEDTOHIGHER-PRMTYPEFAREOR/ANDBUSINESSCOMPARTMENTBYCHARGINGPENALTYOFEUR70ANDTHEDIFFERENCEBETWEENCLASSESPERFARECOMPONENT.--------------------------------------------------FORAUTOMATIC/MANUALREISSUEPENALTYFEEMUSTBECOLLECTEDONANEMDANDMUSTREMAINNONREFUNDABLE.--------------------------------------------------ANYTIMEINCASEOFTICKETUPGRADEANDREFUNDTHEORIGINALNONREFUNDABLEAMOUNTREMAINSNONREFUNDABLE.--------------------------------------------------REROUTINGISNOTPERMITTED--------------------------------------------------NAMECHANGEPERMITTEDATFEE70EURPERTICKETPLUSFAREDIFFERENCEBETWEENCLASSES.CHANGESAFTERDEPARTURECHANGESNOTPERMITTED.NOTE-WHENCOMBININGONAHALFROUNDTRIPBASISTHEPENALTYCONDITIONSFOREACHFARECOMPONENTAPPLY------------------------------------------------IFTHEFLIGHTSEGMENTFOROUTBOUNDTRAVELFROMFARECOMPONENTISUNUSEDTHEFLIGHTSEGMENTFORINBOUNDTRAVELCOULDBEUSEDFORFLIGHTS/DATESASPERPURCHASEDTICKET/ELECTRONICTICKET',
    'Subrules' : [ 
        {
            'Change' : null,
            'Cancellation' : {
                'FareCombination' : 'Sum',
                'Permission' : [ 
                    {
                        'Permitted' : false,
                        'Deadline' : {
                            'NoShow' : true,
                            'AfterDepartureTime' : null,
                            'Value' : null,
                            'Units' : null,
                            'DayOfFlight' : false,
                            'DayBeforeFlight' : false,
                            'DayAfterFlight' : false
                        },
                        'Condition' : null
                    }
                ],
                'Fee' : {
                    'Calculation' : null,
                    'Values' : [ 
                        {
                            'Percents' : null,
                            'Value' : 50,
                            'Ccy' : 'EUR',
                            'Deadline' : null,
                            'Condition' : null
                        }
                    ]
                },
                'FeePer' : null,
                'Taxes' : null,
                'ResidualAmount' : null
            }
        }
    ],
    'CreatedAt' : ISODate('2020-03-20T12:18:53.682Z'),
    'UpdatedAt' : ISODate('2020-06-22T13:47:24.434Z'),
    'UpdatedBy' : {
        '_id' : 'auth0|58907099f1f8405975fd8ba9',
        'Name' : 'Petr Kirillov',
        'Email' : '<EMAIL>',
        'Roles' : [ 
            'system'
        ]
    },
    'Version' : '2',
    'ReservationsCount' : 13,
    'CancellationsCount' : 11,
    'ChangesCount' : 0,
    'AllowRefunds' : true,
    'AllowChanges' : false,
    'IsValidatedForRefunds' : true,
    'IsValidatedForChanges' : false
}",
        ["22e66da8e50bda54540d7c2bd1c724e1"] = @"{
    '_id' : '22e66da8e50bda54540d7c2bd1c724e1',
    'RawText' : 'FOR Y- TYPE FARES\n         NOTE -\n          CANCELLATION\n          UNUSED SECTOR CHARGE 5 PERCENT FOT REFUND/CANCEL\n          MORE THAN 48 HOURS INCLUDING 4 HOURS BEFORE\n          DEPARTURE\n          UNUSED SECTOR CHARGE 10 PERCENT FOT REFUND/CANCEL\n          LESS THAN 48 AND MORE THAN 4 HOURS BEFORE\n          DEPARTURE\n          UNUSED SECTOR CHARGE 15 PERCENT FOT REFUND/CANCEL\n          LESS THAN 4 HOURS INCLUDING 4 HOURS BEFORE\n          DEPARTURE\n          UNUSED SECTOR CHARGE JPY500 FOR REFUND/CANCEL IN\n          JP WHEN ISSUE IATA FARE\n          CHILDREN AND INFANT DISCOUNT DOES NOT APPLY\n          CHANGE\n          FREE OF CHARGE FOR DATE CHANGE MORE THAN 48 HOURS\n          INCLUDING 48 HOURS BEFORE DEPARTURE\n          UNUSED SECTOR CHARGE 5 PERCENT FOR DATE CHANGE\n          LESS THAN 48 HOURS AND MORE THAN 4 HOURS BEFORE\n          DEPARTURE\n          UNUSED SECTOR CHARGE 10 PERCENT FOR DATE CHANGE\n          LESS THAN 4 HOURS BEFORE DEPARTURE\n          CHILDREN AND INFANT DISCOUNT DOES NOT APPLY',
    'NormalizedText' : 'FORY-TYPEFARESNOTE-CANCELLATIONUNUSEDSECTORCHARGE5PERCENTFOTREFUND/CANCELMORETHAN48HOURSINCLUDING4HOURSBEFOREDEPARTUREUNUSEDSECTORCHARGE10PERCENTFOTREFUND/CANCELLESSTHAN48ANDMORETHAN4HOURSBEFOREDEPARTUREUNUSEDSECTORCHARGE15PERCENTFOTREFUND/CANCELLESSTHAN4HOURSINCLUDING4HOURSBEFOREDEPARTUREUNUSEDSECTORCHARGEJPY500FORREFUND/CANCELINJPWHENISSUEIATAFARECHILDRENANDINFANTDISCOUNTDOESNOTAPPLYCHANGEFREEOFCHARGEFORDATECHANGEMORETHAN48HOURSINCLUDING48HOURSBEFOREDEPARTUREUNUSEDSECTORCHARGE5PERCENTFORDATECHANGELESSTHAN48HOURSANDMORETHAN4HOURSBEFOREDEPARTUREUNUSEDSECTORCHARGE10PERCENTFORDATECHANGELESSTHAN4HOURSBEFOREDEPARTURECHILDRENANDINFANTDISCOUNTDOESNOTAPPLY',
    'Subrules' : [ 
        {
            'NoShow' : {
                'Definition' : {
                    'Value' : 4,
                    'Units' : 'Hours'
                },
                'Calculation' : 'Only'
            },
            'Change' : null,
            'Cancellation' : {
                'Permission' : [ 
                    {
                        'Permitted' : true,
                        'Deadline' : {
                            'NoShow' : true
                        }
                    }
                ],
                'Fee' : {
                    'Values' : [ 
                        {
                            'Percents' : 10,
                            'Deadline' : {
                                'NoShow' : false,
                                'Value' : 48,
                                'Units' : 'Hours',
                                'DayOfFlight' : false,
                                'DayBeforeFlight' : false,
                                'DayAfterFlight' : false
                            }
                        }, 
                        {
                            'Percents' : 15,
                            'Deadline' : {
                                'NoShow' : true
                            }
                        }, 
                        {
                            'Percents' : 5
                        }
                    ]
                },
                'FeePer' : [],
                'Taxes' : []
            },
            'Comment' : 'Different rules depending on the time of cancellation:\n CANCELLATION\n          UNUSED SECTOR CHARGE 5 PERCENT FOT REFUND/CANCEL\n          MORE THAN 48 HOURS INCLUDING 4 HOURS BEFORE\n          DEPARTURE\n          UNUSED SECTOR CHARGE 10 PERCENT FOT REFUND/CANCEL\n          LESS THAN 48 AND MORE THAN 4 HOURS BEFORE\n          DEPARTURE\n          UNUSED SECTOR CHARGE 15 PERCENT FOT REFUND/CANCEL\n          LESS THAN 4 HOURS INCLUDING 4 HOURS BEFORE\n          DEPARTURE'
        }
    ],
    'CreatedAt' : ISODate('2019-06-14T08:07:07.965Z'),
    'UpdatedAt' : ISODate('2020-05-18T11:54:07.148Z'),
    'CreatedBy' : {
        '_id' : 'auth0|5a1ebd9ed0998b176385f48e',
        'Name' : '<EMAIL>',
        'Email' : '<EMAIL>',
        'Roles' : null
    },
    'UpdatedBy' : {
        '_id' : 'auth0|58907099f1f8405975fd8ba9',
        'Name' : 'Petr Kirillov',
        'Email' : '<EMAIL>',
        'Roles' : [ 
            'system'
        ]
    },
    'Version' : '2',
    'ReservationsCount' : 4,
    'CancellationsCount' : 3,
    'ChangesCount' : 0,
    'AllowRefunds' : true,
    'AllowChanges' : false,
    'IsValidatedForRefunds' : true,
    'IsValidatedForChanges' : false
}",
        ["e7a511ae903b3f06f39e229249a9303e"] = @"{
    '_id' : 'e7a511ae903b3f06f39e229249a9303e',
    'RawText' : 'UNLESS OTHERWISE SPECIFIED\nCANCELLATIONS\nANY TIME\nCHARGE RUB 2600 FOR CANCEL/NO-SHOW/REFUND.\nCHANGES\nANY TIME\nCHARGE RUB 2600 FOR NO-SHOW/REISSUE/REVALIDATION.\nNOTE -\n// ANYTIME //\nCHANGE PERMITTED ONLY WITHIN SAME BRAND\nOPTIMUM/CLASSIC - FARES WITH PREFIX -CL/-CO/-PX.\n//\n1.WHEN THE FIRST TICKETED FLIGHT COUPON IS\nCHANGED - THE ENTIRE TICKET MUST BE RE-PRICED\nUSING -CURRENT- FARES IN EFFECT ON THE DATE OF NEW\nTICKET ISSUANCE.\n---\nNEW FARE AMOUNT SHOULD BE EQUAL OR HIGHER THAN\nPREVIOUS AMOUNT.\n---\nANY DIFFERENCE IN FARES PLUS CHANGE FEE MUST BE\nCOLLECTED.\n---\nALL RULE PROVISIONS OF THE NEW FARE INCLUDING\nADVANCE PURCHASE/MIN STAY/MAX STAY/\nSEASONALITY/ETC MUST BE MET.\n---\n2. WHEN CHANGES ARE MADE TO OTHER THAN THE FIRST\nTICKETED FLIGHT COUPON - THE ENTIRE TICKET MUST\nBE RE-PRICED USING -HISTORICAL- FARES IN EFFECT\nON THE DATE OF ORIGINAL TICKET ISSUE.\n---\nNEW FARE AMOUNT SHOULD BE EQUAL OR HIGHER THAN\nPREVIOUS AMOUNT.\n---\nANY DIFFERENCE IN FARES PLUS CHANGE FEE MUST BE\nCOLLECTED.\n---\nALL RULE PROVISIONS OF THE NEW FARE INCLUDING\nADVANCE PURCHASE/MIN STAY/MAX STAY/\nSEASONALITY/ETC MUST BE MET.\nUNLESS OTHERWISE SPECIFIED\nNOTE -\n// CANCELLATION PROVISIONS //\n---\nCHARGE APPLY PER TRANSACTION.\nCHILD DISCOUNT DOES NOT APPLY.\nINFANT WITHOUT A SEAT FREE OF CHARGE.\n---\nRULES FOR CANCELLATION APPLY BY PRICING UNIT.\nONLY FARE IN SAME BRAND CAN BE USED FOR REFUND\nPROCEDURE OF PARTLY USED TICKET.\nFARES DIVIDED INTO BRANDS BY FOLLOWING -\nPROMO - FARES WITH PREFIX -SX/-SO\nBUDGET/SAVER - FARES WITH PREFIX -VU/-VO/\nOPTIMUM/CLASSIC - FARES WITH PREFIX -CL/-CO\nMAXIMUM/FLEX - FARES WITH PREFIX -FM/-FO\n//\nWHEN COMBINING ON A HALF ROUNDTRIP BASIS THE MOST\nRESTRICTIVE CANCELLATION CONDITIONS APPLIES FOR\nTHE ENTIRE PRICING UNIT.\n---\nPENALTIES WAIVED IN CASE OF INVOLUNTARY REFUND.\nCONTACT CARRIER FOR DETAILS.\n---\nIF REFUND PERMITTED FLIGHTS SEGMENTS MUST BE\nCANCELLED NO LATER THAN CHECK-IN CLOSE TIME.\nIN SUCH CASE A REFUND MAY BE REQUESTED DURING\nTICKET VALIDITY PERIOD.\nOTHERWISE NO-SHOW PROVISIONS APPLY.\n---\nVOLUNTARY REFUND OF UNUSED FEES AND TAXES\nPERMITTED DURING TICKET VALIDITY PERIOD.\nEXCEPT-\nFOR TICKETS THAT HAVE NO REFUND VALUE AT THE\nMOMENT OF TICKET ISSUE /WHERE FARE IS NON-\nREFUNDABLE ANY TIME/. IN THIS CASE YQ/YR\nSURCHARGES ARE ALSO NON-REFUNDABLE.\n---\nFOR TICKETS THAT HAVE NO REFUND VALUE AT THE\nMOMENT OF TICKET ISSUE /WHERE FARE IS NON-\nREFUNDABLE ANY TIME/IN CASE OF NO-SHOW ALL OTHERS\nTAXES AND SURCHARGES ARE ALSO NON-REFUNDABLE.\n---\nPERIOD VALIDITY FOR SPECIAL FARE WILL BE FARE MAX\nSTAY FROM THE DATE ON THE FIRST FLIGHT COUPON.\n---\nIN CASE OF CANCELLATION AFTER DEPARTURE REFUND\nTHE DIFFERENCE BETWEEN THE FARE PAID AND THE\nAPPLICABLE FARE FLOWN. REFUND FEE APPLIES.\nWHEN RECALCULATING FARES FOR TRANSPORTATION USED\nFARES IN LOWER RBD THAN SHOWN IN USED COUPONS\nCANNOT APPLY.\n---\nFOR EACH USED PORTION OF 9B WITHIN GERMANY 35EUR\nFOR ECONOMY AND 65EUR FOR BUSINESS CLASS OF\nSERVICE SHOULD BE RETAINED FOR PARTLY USED TICKET.\nALL OTHER CHARGES SHOULD BE COLLECTED BASED UPON\nTHE RULE AND FARE USED.\n---\nANY APPLICABLE FARE OF OTHER AIRLINE\nWHOSE PORTION HAD BEEN USED MAY BE APPLIED FOR\nREFUND PROCEDURE OF PARTLY USED TICKET ISSUED IN\nBRAND OPTIMUM/CLASSIC - FARES WITH PREFIX -CL/-CO.\nREFUND CAN ONLY BE MADE THROUGH ISSUING OFFICE.\n---\n// CHANGES PROVISIONS //\n---\nCHARGE APPLY PER TRANSACTION.\nCHILD DISCOUNT DOES NOT APPLY.\nINFANT WITHOUT A SEAT FREE OF CHARGE.\nCHANGE PERMITTED ONLY WITHIN SAME BRAND.\n//EXCEPTION -\nIF CHANGE IS MADE FOR TOTALY UNUSED TICKET BRAND\nLITE CAN BE EXCHANGED TO BRAND SAVER AND BRAND\nSAVER CAN BE EXCHANGED TO BRAND LITE//\nFARES DIVIDED INTO BRANDS BY FOLLOWING -\nPROMO - FARES WITH PREFIX -SX/-SO\nLITE - FARES WITH PREFIX -NB/-NO\nBUDGET/SAVER - FARES WITH PREFIX -VU/-VO/-PX\nOPTIMUM/CLASSIC - FARES WITH PREFIX -CL/-CO\nMAXIMUM/FLEX - FARES WITH PREFIX -FM/-FO\n//\n---\nCHANGE IS A ROUTING/DATE/FLIGHT/BOOKING CLASS/FARE\nMODIFICATION.\n---\nRULES FOR CHANGES APPLY BY FARE\nCOMPONENT/DIRECTION.\nIN CASE OF COMBINATION CHARGE THE HIGHEST FEE OF\nALL CHANGED FARE COMPONENTS.\n---\nWHENEVER A NONREFUNDABLE FARE TICKET IS REISSUED\nA NONREFUNDABLE NOTATION MUST BE MADE IN THE\nENDORSEMENT BOX OF THE NEW TICKET.\nTHE ORIGINAL NONREFUNDABLE VALUE REMAINS\nNONREFUNDABLE FOR ANY SUBSEQUENT REISSUES.\n---\nNEW RESERVATION AND REISSUANCE MUST BE MADE NOT\nLATER THAN 30 MINUTES AFTER DEPARTURE OF THE\nORIGINALLY SCHEDULED FLIGHT.\nOTHERWISE REFER TO REFUND RULES.\nEXCEPTION - FOR FARES WHICH ALLOW TO MAKE CHANGES\nIN CASE OF NO-SHOW REISSUE LATER THAN 30 MINUTES\nAFTER DEPARTURE OF THE\nORIGINALLY SCHEDULED FLIGHT PERMITTED WITHIN\nTICKET VALIDITY PERIOD UNDER THE SAME CONDITIONS\nAS NO-SHOW.',
    'NormalizedText' : 'UNLESSOTHERWISESPECIFIEDCANCELLATIONSANYTIMECHARGERUB2600FORCANCEL/NO-SHOW/REFUND.CHANGESANYTIMECHARGERUB2600FORNO-SHOW/REISSUE/REVALIDATION.NOTE-//ANYTIME//CHANGEPERMITTEDONLYWITHINSAMEBRANDOPTIMUM/CLASSIC-FARESWITHPREFIX-CL/-CO/-PX.//1.WHENTHEFIRSTTICKETEDFLIGHTCOUPONISCHANGED-THEENTIRETICKETMUSTBERE-PRICEDUSING-CURRENT-FARESINEFFECTONTHEDATEOFNEWTICKETISSUANCE.---NEWFAREAMOUNTSHOULDBEEQUALORHIGHERTHANPREVIOUSAMOUNT.---ANYDIFFERENCEINFARESPLUSCHANGEFEEMUSTBECOLLECTED.---ALLRULEPROVISIONSOFTHENEWFAREINCLUDINGADVANCEPURCHASE/MINSTAY/MAXSTAY/SEASONALITY/ETCMUSTBEMET.---2.WHENCHANGESAREMADETOOTHERTHANTHEFIRSTTICKETEDFLIGHTCOUPON-THEENTIRETICKETMUSTBERE-PRICEDUSING-HISTORICAL-FARESINEFFECTONTHEDATEOFORIGINALTICKETISSUE.---NEWFAREAMOUNTSHOULDBEEQUALORHIGHERTHANPREVIOUSAMOUNT.---ANYDIFFERENCEINFARESPLUSCHANGEFEEMUSTBECOLLECTED.---ALLRULEPROVISIONSOFTHENEWFAREINCLUDINGADVANCEPURCHASE/MINSTAY/MAXSTAY/SEASONALITY/ETCMUSTBEMET.UNLESSOTHERWISESPECIFIEDNOTE-//CANCELLATIONPROVISIONS//---CHARGEAPPLYPERTRANSACTION.CHILDDISCOUNTDOESNOTAPPLY.INFANTWITHOUTASEATFREEOFCHARGE.---RULESFORCANCELLATIONAPPLYBYPRICINGUNIT.ONLYFAREINSAMEBRANDCANBEUSEDFORREFUNDPROCEDUREOFPARTLYUSEDTICKET.FARESDIVIDEDINTOBRANDSBYFOLLOWING-PROMO-FARESWITHPREFIX-SX/-SOBUDGET/SAVER-FARESWITHPREFIX-VU/-VO/OPTIMUM/CLASSIC-FARESWITHPREFIX-CL/-COMAXIMUM/FLEX-FARESWITHPREFIX-FM/-FO//WHENCOMBININGONAHALFROUNDTRIPBASISTHEMOSTRESTRICTIVECANCELLATIONCONDITIONSAPPLIESFORTHEENTIREPRICINGUNIT.---PENALTIESWAIVEDINCASEOFINVOLUNTARYREFUND.CONTACTCARRIERFORDETAILS.---IFREFUNDPERMITTEDFLIGHTSSEGMENTSMUSTBECANCELLEDNOLATERTHANCHECK-INCLOSETIME.INSUCHCASEAREFUNDMAYBEREQUESTEDDURINGTICKETVALIDITYPERIOD.OTHERWISENO-SHOWPROVISIONSAPPLY.---VOLUNTARYREFUNDOFUNUSEDFEESANDTAXESPERMITTEDDURINGTICKETVALIDITYPERIOD.EXCEPT-FORTICKETSTHATHAVENOREFUNDVALUEATTHEMOMENTOFTICKETISSUE/WHEREFAREISNON-REFUNDABLEANYTIME/.INTHISCASEYQ/YRSURCHARGESAREALSONON-REFUNDABLE.---FORTICKETSTHATHAVENOREFUNDVALUEATTHEMOMENTOFTICKETISSUE/WHEREFAREISNON-REFUNDABLEANYTIME/INCASEOFNO-SHOWALLOTHERSTAXESANDSURCHARGESAREALSONON-REFUNDABLE.---PERIODVALIDITYFORSPECIALFAREWILLBEFAREMAXSTAYFROMTHEDATEONTHEFIRSTFLIGHTCOUPON.---INCASEOFCANCELLATIONAFTERDEPARTUREREFUNDTHEDIFFERENCEBETWEENTHEFAREPAIDANDTHEAPPLICABLEFAREFLOWN.REFUNDFEEAPPLIES.WHENRECALCULATINGFARESFORTRANSPORTATIONUSEDFARESINLOWERRBDTHANSHOWNINUSEDCOUPONSCANNOTAPPLY.---FOREACHUSEDPORTIONOF9BWITHINGERMANY35EURFORECONOMYAND65EURFORBUSINESSCLASSOFSERVICESHOULDBERETAINEDFORPARTLYUSEDTICKET.ALLOTHERCHARGESSHOULDBECOLLECTEDBASEDUPONTHERULEANDFAREUSED.---ANYAPPLICABLEFAREOFOTHERAIRLINEWHOSEPORTIONHADBEENUSEDMAYBEAPPLIEDFORREFUNDPROCEDUREOFPARTLYUSEDTICKETISSUEDINBRANDOPTIMUM/CLASSIC-FARESWITHPREFIX-CL/-CO.REFUNDCANONLYBEMADETHROUGHISSUINGOFFICE.---//CHANGESPROVISIONS//---CHARGEAPPLYPERTRANSACTION.CHILDDISCOUNTDOESNOTAPPLY.INFANTWITHOUTASEATFREEOFCHARGE.CHANGEPERMITTEDONLYWITHINSAMEBRAND.//EXCEPTION-IFCHANGEISMADEFORTOTALYUNUSEDTICKETBRANDLITECANBEEXCHANGEDTOBRANDSAVERANDBRANDSAVERCANBEEXCHANGEDTOBRANDLITE//FARESDIVIDEDINTOBRANDSBYFOLLOWING-PROMO-FARESWITHPREFIX-SX/-SOLITE-FARESWITHPREFIX-NB/-NOBUDGET/SAVER-FARESWITHPREFIX-VU/-VO/-PXOPTIMUM/CLASSIC-FARESWITHPREFIX-CL/-COMAXIMUM/FLEX-FARESWITHPREFIX-FM/-FO//---CHANGEISAROUTING/DATE/FLIGHT/BOOKINGCLASS/FAREMODIFICATION.---RULESFORCHANGESAPPLYBYFARECOMPONENT/DIRECTION.INCASEOFCOMBINATIONCHARGETHEHIGHESTFEEOFALLCHANGEDFARECOMPONENTS.---WHENEVERANONREFUNDABLEFARETICKETISREISSUEDANONREFUNDABLENOTATIONMUSTBEMADEINTHEENDORSEMENTBOXOFTHENEWTICKET.THEORIGINALNONREFUNDABLEVALUEREMAINSNONREFUNDABLEFORANYSUBSEQUENTREISSUES.---NEWRESERVATIONANDREISSUANCEMUSTBEMADENOTLATERTHAN30MINUTESAFTERDEPARTUREOFTHEORIGINALLYSCHEDULEDFLIGHT.OTHERWISEREFERTOREFUNDRULES.EXCEPTION-FORFARESWHICHALLOWTOMAKECHANGESINCASEOFNO-SHOWREISSUELATERTHAN30MINUTESAFTERDEPARTUREOFTHEORIGINALLYSCHEDULEDFLIGHTPERMITTEDWITHINTICKETVALIDITYPERIODUNDERTHESAMECONDITIONSASNO-SHOW.',
    'Subrules' : [ 
        {
            'Change' : null,
            'Cancellation' : {
                'FareCombination' : 'Strictest',
                'Fee' : {
                    'Values' : [ 
                        {
                            'Value' : 2600,
                            'Ccy' : 'RUB'
                        }
                    ]
                },
                'Taxes' : [ 
                    {
                        'Name' : 'YQ'
                    }
                ]
            }
        }
    ],
    'CreatedAt' : ISODate('2020-02-11T14:17:01.816Z'),
    'UpdatedAt' : ISODate('2020-04-01T15:57:44.598Z'),
    'UpdatedBy' : {
        '_id' : 'auth0|5919670e6e197c4a0c44bd5f',
        'Name' : 'Rost',
        'Email' : '<EMAIL>',
        'Roles' : [ 
            'system'
        ]
    },
    'Version' : '2',
    'ReservationsCount' : 3,
    'CancellationsCount' : 0,
    'ChangesCount' : 0,
    'AllowRefunds' : true,
    'AllowChanges' : false,
    'IsValidatedForRefunds' : true,
    'IsValidatedForChanges' : false
}",
        ["27ccd847abc0210b5a700fc261502379"] = @"{
    '_id' : '27ccd847abc0210b5a700fc261502379',
    'RawText' : 'PE.PENALTIES\n \n  CHANGES/CANCELLATIONS\n \n    ANY TIME\n      TICKET IS NON-REFUNDABLE. CHANGES NOT PERMITTED.\n         NOTE -\n          WHEN COMBINING ON A HALF ROUNDTRIP BASIS THE\n          PENALTY CONDITIONS FOR EACH FARE COMPONENT APPLY\n          --------------------------------------------------\n          YQ/YR CHARGES ARE NON-REFUNDABLE\n          --------------------------------------------------\n          IF OB FEE IS USED AS PART OF FORM OF PAYMENT THEN\n          ALSO OB FEES ARE NON-REFUNDABLE\n          --------------------------------------------------\n          REFUND OF UNUSED TAXES PERMITTED FOR FULLY UNUSED\n          FARE COMPONENT.IF PART OF FARE COMPONENT IS USED -\n          IN THIS CASE NO FARE/TAX REFUND WILL BE PERMITTED.\n          --------------------------------------------------\n          NAME CHANGE NOT PERMITTED\n          --------------------------------------------------\n          IF THE FLIGHT SEGMENT FOR OUTBOUND TRAVEL FROM\n          FARE COMPONENT IS UNUSED THE FLIGHT SEGMENT FOR\n          INBOUND TRAVEL COULD BE USED FOR FLIGHTS/DATES\n          AS PER PURCHASED TICKET/ELECTRONIC TICKET',
    'NormalizedText' : 'PE.PENALTIESCHANGES/CANCELLATIONSANYTIMETICKETISNON-REFUNDABLE.CHANGESNOTPERMITTED.NOTE-WHENCOMBININGONAHALFROUNDTRIPBASISTHEPENALTYCONDITIONSFOREACHFARECOMPONENTAPPLY--------------------------------------------------YQ/YRCHARGESARENON-REFUNDABLE--------------------------------------------------IFOBFEEISUSEDASPARTOFFORMOFPAYMENTTHENALSOOBFEESARENON-REFUNDABLE--------------------------------------------------REFUNDOFUNUSEDTAXESPERMITTEDFORFULLYUNUSEDFARECOMPONENT.IFPARTOFFARECOMPONENTISUSED-INTHISCASENOFARE/TAXREFUNDWILLBEPERMITTED.--------------------------------------------------NAMECHANGENOTPERMITTED--------------------------------------------------IFTHEFLIGHTSEGMENTFOROUTBOUNDTRAVELFROMFARECOMPONENTISUNUSEDTHEFLIGHTSEGMENTFORINBOUNDTRAVELCOULDBEUSEDFORFLIGHTS/DATESASPERPURCHASEDTICKET/ELECTRONICTICKET',
    'Subrules' : [ 
        {
            'Change' : {
                'Permission' : [ 
                    {
                        'Permitted' : false
                    }
                ]
            },
            'Cancellation' : {
                'FareCombination' : null,
                'Permission' : [ 
                    {
                        'Permitted' : false,
                        'Deadline' : null,
                        'Condition' : null
                    }
                ],
                'Fee' : null,
                'FeePer' : null,
                'Taxes' : null,
                'ResidualAmount' : null
            }
        }
    ],
    'CreatedAt' : ISODate('2020-01-29T14:16:07.526Z'),
    'UpdatedAt' : ISODate('2020-02-14T16:19:43.897Z'),
    'UpdatedBy' : {
        '_id' : 'auth0|5919670e6e197c4a0c44bd5f',
        'Name' : 'Rost',
        'Email' : '<EMAIL>',
        'Roles' : [ 
            'system'
        ]
    },
    'Version' : '2',
    'ReservationsCount' : 811,
    'CancellationsCount' : 433,
    'ChangesCount' : 0,
    'AllowRefunds' : true,
    'AllowChanges' : true,
    'IsValidatedForRefunds' : true,
    'IsValidatedForChanges' : true
}",
        ["20f15a75441d15d005834d7b8c55b270"] = @"{
    '_id' : '20f15a75441d15d005834d7b8c55b270',
    'RawText' : 'UNLESS OTHERWISE SPECIFIED\nCANCELLATIONS\nANY TIME\nCANCELLATIONS PERMITTED FOR CANCEL/REFUND.\nNOTE -\nWHEN COMBINING ON A HALF ROUNDTRIP BASIS THE\nPENALTY CONDITIONS FOR EACH FARE COMPONENT APPLY.\n--------------------------------------------------\nFOR PARTIALLY USED TICKET -\nREFUND THE DIFFERENCE BETWEEN THE FARE PAID AND\nAPPLICABLE - EQUAL OR HIGHER - BT ONEWAY FARE\nFOR THE SECTOR FLOWN.\n--------------------------------------------------\nWHEN COMBINING NON-REFUNDABLE FARES WITH\nREFUNDABLE FARES\n- THE AMOUNT PAID ON EACH REFUNDABLE FARE\nCOMPONENT IS REFUNDED\n- THE AMOUNT PAID ON EACH NON-REFUNDABLE FARE\nCOMPONENT WILL NOT BE REFUNDED\n- WHEN COMBINING FARES CHARGE THE SUM OF THE\nCANCELLATION FEES OF ALL CANCELLED FARE COMPONENTS\n--------------------------------------------------\nIF OB FEE IS USED AS PART OF FORM OF PAYMENT THEN\nALSO OB FEES ARE REFUNDABLE.\n--------------------------------------------------\nREFUND OF UNUSED TAXES PERMITTED FOR FULLY UNUSED\nFARE COMPONENT.IF PART OF FARE COMPONENT IS USED -\nIN THIS CASE NO FARE/TAX REFUND WILL BE PERMITTED.\nCHANGES\nANY TIME\nCHANGES PERMITTED.\nNOTE -\nUPGRADE -\nPERMITTED WITHIN BUSINESS CLASS COMPARTMENT TO\nNEXT AVAILABLE BOOKING CLASS BY CHARGING\nDIFFERENCE BETWEEN THE CLASSES PER FARE COMPONENT\n--------------------\nANY TIME\nIN CASE OF TICKET UPGRADE AND REFUND THE ORIGINAL\nNONREFUNDABLE AMOUNT REMAINS NONREFUNDABLE.\n--------------------\nREROUTING IS NOT PERMITTED\n-------------------------------------------------\nNAME CHANGE PERMITTED. PLEASE CONTACT BT FOR MORE\nDETAILS.\n-------------------------------------------------\nIF THE FLIGHT SEGMENT FOR OUTBOUND TRAVEL FROM\nFARE COMPONENT IS UNUSED THE FLIGHT SEGMENT FOR\nINBOUND TRAVEL COULD BE USED FOR FLIGHTS/DATES\nAS PER PURCHASED TICKET/ELECTRONIC TICKET',
    'NormalizedText' : 'UNLESSOTHERWISESPECIFIEDCANCELLATIONSANYTIMECANCELLATIONSPERMITTEDFORCANCEL/REFUND.NOTE-WHENCOMBININGONAHALFROUNDTRIPBASISTHEPENALTYCONDITIONSFOREACHFARECOMPONENTAPPLY.--------------------------------------------------FORPARTIALLYUSEDTICKET-REFUNDTHEDIFFERENCEBETWEENTHEFAREPAIDANDAPPLICABLE-EQUALORHIGHER-BTONEWAYFAREFORTHESECTORFLOWN.--------------------------------------------------WHENCOMBININGNON-REFUNDABLEFARESWITHREFUNDABLEFARES-THEAMOUNTPAIDONEACHREFUNDABLEFARECOMPONENTISREFUNDED-THEAMOUNTPAIDONEACHNON-REFUNDABLEFARECOMPONENTWILLNOTBEREFUNDED-WHENCOMBININGFARESCHARGETHESUMOFTHECANCELLATIONFEESOFALLCANCELLEDFARECOMPONENTS--------------------------------------------------IFOBFEEISUSEDASPARTOFFORMOFPAYMENTTHENALSOOBFEESAREREFUNDABLE.--------------------------------------------------REFUNDOFUNUSEDTAXESPERMITTEDFORFULLYUNUSEDFARECOMPONENT.IFPARTOFFARECOMPONENTISUSED-INTHISCASENOFARE/TAXREFUNDWILLBEPERMITTED.CHANGESANYTIMECHANGESPERMITTED.NOTE-UPGRADE-PERMITTEDWITHINBUSINESSCLASSCOMPARTMENTTONEXTAVAILABLEBOOKINGCLASSBYCHARGINGDIFFERENCEBETWEENTHECLASSESPERFARECOMPONENT--------------------ANYTIMEINCASEOFTICKETUPGRADEANDREFUNDTHEORIGINALNONREFUNDABLEAMOUNTREMAINSNONREFUNDABLE.--------------------REROUTINGISNOTPERMITTED-------------------------------------------------NAMECHANGEPERMITTED.PLEASECONTACTBTFORMOREDETAILS.-------------------------------------------------IFTHEFLIGHTSEGMENTFOROUTBOUNDTRAVELFROMFARECOMPONENTISUNUSEDTHEFLIGHTSEGMENTFORINBOUNDTRAVELCOULDBEUSEDFORFLIGHTS/DATESASPERPURCHASEDTICKET/ELECTRONICTICKET',
    'Subrules' : null,
    'CreatedAt' : ISODate('2020-02-13T11:25:31.028Z'),
    'Version' : '1',
    'ReservationsCount' : 3,
    'CancellationsCount' : 3,
    'ChangesCount' : 0,
    'AllowRefunds' : false,
    'AllowChanges' : false,
    'IsValidatedForRefunds' : false,
    'IsValidatedForChanges' : false
}",
        ["c7f5e6d81a326f073a9a5b3baa409be7"] = @"{
    '_id' : 'c7f5e6d81a326f073a9a5b3baa409be7',
    'RawText' : 'BETWEEN EUROPE AND MIDDLE EAST\nORIGINATING EUROPE -\nCANCELLATIONS\nANY TIME\nCHARGE EUR 37.00 FOR CANCEL/REFUND.\nTICKET IS NON-REFUNDABLE IN CASE OF NO-SHOW.\nCHANGES\nANY TIME\nCHARGE EUR 37.00 FOR REISSUE/REVALIDATION.\nCHANGES NOT PERMITTED IN CASE OF NO-SHOW.\nNOTE -\n//NOT LATER THAN 30 MINUTES AFTER DEPARTURE OF THE\nORIGINALLY SCHEDULED FLIGHT.                  //\nCHANGE PERMITTED ONLY WITHIN SAME BRAND\nOPTIMUM/CLASSIC - FARES WITH PREFIX -CL/-CO/-PX.\n//\n1.WHEN THE FIRST TICKETED FLIGHT COUPON IS\nCHANGED - THE ENTIRE TICKET MUST BE RE-PRICED\nUSING -CURRENT- FARES IN EFFECT ON THE DATE OF NEW\nTICKET ISSUANCE.\n---\nNEW FARE AMOUNT SHOULD BE EQUAL OR HIGHER THAN\nPREVIOUS AMOUNT.\n---\nANY DIFFERENCE IN FARES PLUS CHANGE FEE MUST BE\nCOLLECTED.\n---\nALL RULE PROVISIONS OF THE NEW FARE INCLUDING\nADVANCE PURCHASE/MIN STAY/MAX STAY/\nSEASONALITY/ETC MUST BE MET.\n---\n2. WHEN CHANGES ARE MADE TO OTHER THAN THE FIRST\nTICKETED FLIGHT COUPON - THE ENTIRE TICKET MUST\nBE RE-PRICED USING -HISTORICAL- FARES IN EFFECT\nON THE DATE OF ORIGINAL TICKET ISSUE.\n---\nNEW FARE AMOUNT SHOULD BE EQUAL OR HIGHER THAN\nPREVIOUS AMOUNT.\n---\nANY DIFFERENCE IN FARES PLUS CHANGE FEE MUST BE\nCOLLECTED.\n---\nALL RULE PROVISIONS OF THE NEW FARE INCLUDING\nADVANCE PURCHASE/MIN STAY/MAX STAY/\nSEASONALITY/ETC MUST BE MET.\nORIGINATING MIDDLE EAST -\nCANCELLATIONS\nANY TIME\nCHARGE USD 42.00 FOR CANCEL/REFUND.\nCHARGE USD 85.00 FOR NO-SHOW.\nCHANGES\nANY TIME\nCHARGE USD 42.00 FOR REISSUE/REVALIDATION.\nCHARGE USD 85.00 FOR NO-SHOW.\nNOTE -\n// ANYTIME //\nCHANGE PERMITTED ONLY WITHIN SAME BRAND\nOPTIMUM/CLASSIC - FARES WITH PREFIX -CL/-CO/-PX.\n//\n1.WHEN THE FIRST TICKETED FLIGHT COUPON IS\nCHANGED - THE ENTIRE TICKET MUST BE RE-PRICED\nUSING -CURRENT- FARES IN EFFECT ON THE DATE OF NEW\nTICKET ISSUANCE.\n---\nNEW FARE AMOUNT SHOULD BE EQUAL OR HIGHER THAN\nPREVIOUS AMOUNT.\n---\nANY DIFFERENCE IN FARES PLUS CHANGE FEE MUST BE\nCOLLECTED.\n---\nALL RULE PROVISIONS OF THE NEW FARE INCLUDING\nADVANCE PURCHASE/MIN STAY/MAX STAY/\nSEASONALITY/ETC MUST BE MET.\n---\n2. WHEN CHANGES ARE MADE TO OTHER THAN THE FIRST\nTICKETED FLIGHT COUPON - THE ENTIRE TICKET MUST\nBE RE-PRICED USING -HISTORICAL- FARES IN EFFECT\nON THE DATE OF ORIGINAL TICKET ISSUE.\n---\nNEW FARE AMOUNT SHOULD BE EQUAL OR HIGHER THAN\nPREVIOUS AMOUNT.\n---\nANY DIFFERENCE IN FARES PLUS CHANGE FEE MUST BE\nCOLLECTED.\n---\nALL RULE PROVISIONS OF THE NEW FARE INCLUDING\nADVANCE PURCHASE/MIN STAY/MAX STAY/\nSEASONALITY/ETC MUST BE MET.\nUNLESS OTHERWISE SPECIFIED\nNOTE -\n// CANCELLATION PROVISIONS //\n---\nCHARGE APPLY PER TRANSACTION.\nCHILD DISCOUNT DOES NOT APPLY.\nINFANT WITHOUT A SEAT FREE OF CHARGE.\n---\nRULES FOR CANCELLATION APPLY BY PRICING UNIT.\nONLY FARE IN SAME BRAND CAN BE USED FOR REFUND\nPROCEDURE OF PARTLY USED TICKET.\nFARES DIVIDED INTO BRANDS BY FOLLOWING -\nPROMO - FARES WITH PREFIX -SX/-SO\nBUDGET/SAVER - FARES WITH PREFIX -VU/-VO/\nOPTIMUM/CLASSIC - FARES WITH PREFIX -CL/-CO\nMAXIMUM/FLEX - FARES WITH PREFIX -FM/-FO\n//\nWHEN COMBINING ON A HALF ROUNDTRIP BASIS THE MOST\nRESTRICTIVE CANCELLATION CONDITIONS APPLIES FOR\nTHE ENTIRE PRICING UNIT.\n---\nPENALTIES WAIVED IN CASE OF INVOLUNTARY REFUND.\nCONTACT CARRIER FOR DETAILS.\n---\nIF REFUND PERMITTED FLIGHTS SEGMENTS MUST BE\nCANCELLED NO LATER THAN CHECK-IN CLOSE TIME.\nIN SUCH CASE A REFUND MAY BE REQUESTED DURING\nTICKET VALIDITY PERIOD.\nOTHERWISE NO-SHOW PROVISIONS APPLY.\n---\nVOLUNTARY REFUND OF UNUSED FEES AND TAXES\nPERMITTED DURING TICKET VALIDITY PERIOD.\nEXCEPT-\nFOR TICKETS THAT HAVE NO REFUND VALUE AT THE\nMOMENT OF TICKET ISSUE /WHERE FARE IS NON-\nREFUNDABLE ANY TIME/. IN THIS CASE YQ/YR\nSURCHARGES ARE ALSO NON-REFUNDABLE.\n---\nFOR TICKETS THAT HAVE NO REFUND VALUE AT THE\nMOMENT OF TICKET ISSUE /WHERE FARE IS NON-\nREFUNDABLE ANY TIME/IN CASE OF NO-SHOW ALL OTHERS\nTAXES AND SURCHARGES ARE ALSO NON-REFUNDABLE.\n---\nPERIOD VALIDITY FOR SPECIAL FARE WILL BE FARE MAX\nSTAY FROM THE DATE ON THE FIRST FLIGHT COUPON.\n---\nIN CASE OF CANCELLATION AFTER DEPARTURE REFUND\nTHE DIFFERENCE BETWEEN THE FARE PAID AND THE\nAPPLICABLE FARE FLOWN. REFUND FEE APPLIES.\nWHEN RECALCULATING FARES FOR TRANSPORTATION USED\nFARES IN LOWER RBD THAN SHOWN IN USED COUPONS\nCANNOT APPLY.\n---\nFOR EACH USED PORTION OF 9B WITHIN GERMANY 35EUR\nFOR ECONOMY AND 65EUR FOR BUSINESS CLASS OF\nSERVICE SHOULD BE RETAINED FOR PARTLY USED TICKET.\nALL OTHER CHARGES SHOULD BE COLLECTED BASED UPON\nTHE RULE AND FARE USED.\n---\nANY APPLICABLE FARE OF OTHER AIRLINE\nWHOSE PORTION HAD BEEN USED MAY BE APPLIED FOR\nREFUND PROCEDURE OF PARTLY USED TICKET ISSUED IN\nBRAND OPTIMUM/CLASSIC - FARES WITH PREFIX -CL/-CO.\nREFUND CAN ONLY BE MADE THROUGH ISSUING OFFICE.\n---\n// CHANGES PROVISIONS //\n---\nCHARGE APPLY PER TRANSACTION.\nCHILD DISCOUNT DOES NOT APPLY.\nINFANT WITHOUT A SEAT FREE OF CHARGE.\nCHANGE PERMITTED ONLY WITHIN SAME BRAND.\n//EXCEPTION -\nIF CHANGE IS MADE FOR TOTALY UNUSED TICKET BRAND\nLITE CAN BE EXCHANGED TO BRAND SAVER AND BRAND\nSAVER CAN BE EXCHANGED TO BRAND LITE//\nFARES DIVIDED INTO BRANDS BY FOLLOWING -\nPROMO - FARES WITH PREFIX -SX/-SO\nLITE - FARES WITH PREFIX -NB/-NO\nBUDGET/SAVER - FARES WITH PREFIX -VU/-VO/-PX\nOPTIMUM/CLASSIC - FARES WITH PREFIX -CL/-CO\nMAXIMUM/FLEX - FARES WITH PREFIX -FM/-FO\n//\n---\nCHANGE IS A ROUTING/DATE/FLIGHT/BOOKING CLASS/FARE\nMODIFICATION.\n---\nRULES FOR CHANGES APPLY BY FARE\nCOMPONENT/DIRECTION.\nIN CASE OF COMBINATION CHARGE THE HIGHEST FEE OF\nALL CHANGED FARE COMPONENTS.\n---\nWHENEVER A NONREFUNDABLE FARE TICKET IS REISSUED\nA NONREFUNDABLE NOTATION MUST BE MADE IN THE\nENDORSEMENT BOX OF THE NEW TICKET.\nTHE ORIGINAL NONREFUNDABLE VALUE REMAINS\nNONREFUNDABLE FOR ANY SUBSEQUENT REISSUES.\n---\nNEW RESERVATION AND REISSUANCE MUST BE MADE NOT\nLATER THAN 30 MINUTES AFTER DEPARTURE OF THE\nORIGINALLY SCHEDULED FLIGHT.\nOTHERWISE REFER TO REFUND RULES.\nEXCEPTION - FOR FARES WHICH ALLOW TO MAKE CHANGES\nIN CASE OF NO-SHOW REISSUE LATER THAN 30 MINUTES\nAFTER DEPARTURE OF THE\nORIGINALLY SCHEDULED FLIGHT PERMITTED WITHIN\nTICKET VALIDITY PERIOD UNDER THE SAME CONDITIONS\nAS NO-SHOW.',
    'NormalizedText' : 'BETWEENEUROPEANDMIDDLEEASTORIGINATINGEUROPE-CANCELLATIONSANYTIMECHARGEEUR37.00FORCANCEL/REFUND.TICKETISNON-REFUNDABLEINCASEOFNO-SHOW.CHANGESANYTIMECHARGEEUR37.00FORREISSUE/REVALIDATION.CHANGESNOTPERMITTEDINCASEOFNO-SHOW.NOTE-//NOTLATERTHAN30MINUTESAFTERDEPARTUREOFTHEORIGINALLYSCHEDULEDFLIGHT.//CHANGEPERMITTEDONLYWITHINSAMEBRANDOPTIMUM/CLASSIC-FARESWITHPREFIX-CL/-CO/-PX.//1.WHENTHEFIRSTTICKETEDFLIGHTCOUPONISCHANGED-THEENTIRETICKETMUSTBERE-PRICEDUSING-CURRENT-FARESINEFFECTONTHEDATEOFNEWTICKETISSUANCE.---NEWFAREAMOUNTSHOULDBEEQUALORHIGHERTHANPREVIOUSAMOUNT.---ANYDIFFERENCEINFARESPLUSCHANGEFEEMUSTBECOLLECTED.---ALLRULEPROVISIONSOFTHENEWFAREINCLUDINGADVANCEPURCHASE/MINSTAY/MAXSTAY/SEASONALITY/ETCMUSTBEMET.---2.WHENCHANGESAREMADETOOTHERTHANTHEFIRSTTICKETEDFLIGHTCOUPON-THEENTIRETICKETMUSTBERE-PRICEDUSING-HISTORICAL-FARESINEFFECTONTHEDATEOFORIGINALTICKETISSUE.---NEWFAREAMOUNTSHOULDBEEQUALORHIGHERTHANPREVIOUSAMOUNT.---ANYDIFFERENCEINFARESPLUSCHANGEFEEMUSTBECOLLECTED.---ALLRULEPROVISIONSOFTHENEWFAREINCLUDINGADVANCEPURCHASE/MINSTAY/MAXSTAY/SEASONALITY/ETCMUSTBEMET.ORIGINATINGMIDDLEEAST-CANCELLATIONSANYTIMECHARGEUSD42.00FORCANCEL/REFUND.CHARGEUSD85.00FORNO-SHOW.CHANGESANYTIMECHARGEUSD42.00FORREISSUE/REVALIDATION.CHARGEUSD85.00FORNO-SHOW.NOTE-//ANYTIME//CHANGEPERMITTEDONLYWITHINSAMEBRANDOPTIMUM/CLASSIC-FARESWITHPREFIX-CL/-CO/-PX.//1.WHENTHEFIRSTTICKETEDFLIGHTCOUPONISCHANGED-THEENTIRETICKETMUSTBERE-PRICEDUSING-CURRENT-FARESINEFFECTONTHEDATEOFNEWTICKETISSUANCE.---NEWFAREAMOUNTSHOULDBEEQUALORHIGHERTHANPREVIOUSAMOUNT.---ANYDIFFERENCEINFARESPLUSCHANGEFEEMUSTBECOLLECTED.---ALLRULEPROVISIONSOFTHENEWFAREINCLUDINGADVANCEPURCHASE/MINSTAY/MAXSTAY/SEASONALITY/ETCMUSTBEMET.---2.WHENCHANGESAREMADETOOTHERTHANTHEFIRSTTICKETEDFLIGHTCOUPON-THEENTIRETICKETMUSTBERE-PRICEDUSING-HISTORICAL-FARESINEFFECTONTHEDATEOFORIGINALTICKETISSUE.---NEWFAREAMOUNTSHOULDBEEQUALORHIGHERTHANPREVIOUSAMOUNT.---ANYDIFFERENCEINFARESPLUSCHANGEFEEMUSTBECOLLECTED.---ALLRULEPROVISIONSOFTHENEWFAREINCLUDINGADVANCEPURCHASE/MINSTAY/MAXSTAY/SEASONALITY/ETCMUSTBEMET.UNLESSOTHERWISESPECIFIEDNOTE-//CANCELLATIONPROVISIONS//---CHARGEAPPLYPERTRANSACTION.CHILDDISCOUNTDOESNOTAPPLY.INFANTWITHOUTASEATFREEOFCHARGE.---RULESFORCANCELLATIONAPPLYBYPRICINGUNIT.ONLYFAREINSAMEBRANDCANBEUSEDFORREFUNDPROCEDUREOFPARTLYUSEDTICKET.FARESDIVIDEDINTOBRANDSBYFOLLOWING-PROMO-FARESWITHPREFIX-SX/-SOBUDGET/SAVER-FARESWITHPREFIX-VU/-VO/OPTIMUM/CLASSIC-FARESWITHPREFIX-CL/-COMAXIMUM/FLEX-FARESWITHPREFIX-FM/-FO//WHENCOMBININGONAHALFROUNDTRIPBASISTHEMOSTRESTRICTIVECANCELLATIONCONDITIONSAPPLIESFORTHEENTIREPRICINGUNIT.---PENALTIESWAIVEDINCASEOFINVOLUNTARYREFUND.CONTACTCARRIERFORDETAILS.---IFREFUNDPERMITTEDFLIGHTSSEGMENTSMUSTBECANCELLEDNOLATERTHANCHECK-INCLOSETIME.INSUCHCASEAREFUNDMAYBEREQUESTEDDURINGTICKETVALIDITYPERIOD.OTHERWISENO-SHOWPROVISIONSAPPLY.---VOLUNTARYREFUNDOFUNUSEDFEESANDTAXESPERMITTEDDURINGTICKETVALIDITYPERIOD.EXCEPT-FORTICKETSTHATHAVENOREFUNDVALUEATTHEMOMENTOFTICKETISSUE/WHEREFAREISNON-REFUNDABLEANYTIME/.INTHISCASEYQ/YRSURCHARGESAREALSONON-REFUNDABLE.---FORTICKETSTHATHAVENOREFUNDVALUEATTHEMOMENTOFTICKETISSUE/WHEREFAREISNON-REFUNDABLEANYTIME/INCASEOFNO-SHOWALLOTHERSTAXESANDSURCHARGESAREALSONON-REFUNDABLE.---PERIODVALIDITYFORSPECIALFAREWILLBEFAREMAXSTAYFROMTHEDATEONTHEFIRSTFLIGHTCOUPON.---INCASEOFCANCELLATIONAFTERDEPARTUREREFUNDTHEDIFFERENCEBETWEENTHEFAREPAIDANDTHEAPPLICABLEFAREFLOWN.REFUNDFEEAPPLIES.WHENRECALCULATINGFARESFORTRANSPORTATIONUSEDFARESINLOWERRBDTHANSHOWNINUSEDCOUPONSCANNOTAPPLY.---FOREACHUSEDPORTIONOF9BWITHINGERMANY35EURFORECONOMYAND65EURFORBUSINESSCLASSOFSERVICESHOULDBERETAINEDFORPARTLYUSEDTICKET.ALLOTHERCHARGESSHOULDBECOLLECTEDBASEDUPONTHERULEANDFAREUSED.---ANYAPPLICABLEFAREOFOTHERAIRLINEWHOSEPORTIONHADBEENUSEDMAYBEAPPLIEDFORREFUNDPROCEDUREOFPARTLYUSEDTICKETISSUEDINBRANDOPTIMUM/CLASSIC-FARESWITHPREFIX-CL/-CO.REFUNDCANONLYBEMADETHROUGHISSUINGOFFICE.---//CHANGESPROVISIONS//---CHARGEAPPLYPERTRANSACTION.CHILDDISCOUNTDOESNOTAPPLY.INFANTWITHOUTASEATFREEOFCHARGE.CHANGEPERMITTEDONLYWITHINSAMEBRAND.//EXCEPTION-IFCHANGEISMADEFORTOTALYUNUSEDTICKETBRANDLITECANBEEXCHANGEDTOBRANDSAVERANDBRANDSAVERCANBEEXCHANGEDTOBRANDLITE//FARESDIVIDEDINTOBRANDSBYFOLLOWING-PROMO-FARESWITHPREFIX-SX/-SOLITE-FARESWITHPREFIX-NB/-NOBUDGET/SAVER-FARESWITHPREFIX-VU/-VO/-PXOPTIMUM/CLASSIC-FARESWITHPREFIX-CL/-COMAXIMUM/FLEX-FARESWITHPREFIX-FM/-FO//---CHANGEISAROUTING/DATE/FLIGHT/BOOKINGCLASS/FAREMODIFICATION.---RULESFORCHANGESAPPLYBYFARECOMPONENT/DIRECTION.INCASEOFCOMBINATIONCHARGETHEHIGHESTFEEOFALLCHANGEDFARECOMPONENTS.---WHENEVERANONREFUNDABLEFARETICKETISREISSUEDANONREFUNDABLENOTATIONMUSTBEMADEINTHEENDORSEMENTBOXOFTHENEWTICKET.THEORIGINALNONREFUNDABLEVALUEREMAINSNONREFUNDABLEFORANYSUBSEQUENTREISSUES.---NEWRESERVATIONANDREISSUANCEMUSTBEMADENOTLATERTHAN30MINUTESAFTERDEPARTUREOFTHEORIGINALLYSCHEDULEDFLIGHT.OTHERWISEREFERTOREFUNDRULES.EXCEPTION-FORFARESWHICHALLOWTOMAKECHANGESINCASEOFNO-SHOWREISSUELATERTHAN30MINUTESAFTERDEPARTUREOFTHEORIGINALLYSCHEDULEDFLIGHTPERMITTEDWITHINTICKETVALIDITYPERIODUNDERTHESAMECONDITIONSASNO-SHOW.',
    'Subrules' : null,
    'CreatedAt' : ISODate('2020-03-05T10:54:21.238Z'),
    'Version' : '1',
    'ReservationsCount' : 1,
    'CancellationsCount' : 1,
    'ChangesCount' : 0,
    'AllowRefunds' : false,
    'AllowChanges' : false,
    'IsValidatedForRefunds' : false,
    'IsValidatedForChanges' : false
}",
        ["ef53b3a4a4a4889f5b55e4c810aa83b2"] = @"{
    '_id' : 'ef53b3a4a4a4889f5b55e4c810aa83b2',
    'RawText' : 'FULLY REFUNDABLE FARE FOR TESTING SUPPORTED FLAG IN CRUNCHER',
    'NormalizedText' : 'FULLYREFUNDABLEFAREFORTESTINGSUPPORTEDFLAGINCRUNCHER',
    'Subrules' : [ 
        {
            'Change' : null,
            'Cancellation' : null
        }
    ],
    'CreatedAt' : ISODate('2020-03-05T10:54:21.238Z'),
    'Version' : '1',
    'ReservationsCount' : 1,
    'CancellationsCount' : 1,
    'ChangesCount' : 0,
    'AllowRefunds' : false,
    'AllowChanges' : false,
    'IsValidatedForRefunds' : true,
    'IsValidatedForChanges' : false
}",
        ["d9d6f84664ca6e59b6e713ea079118ee"] = @"{
    '_id' : 'd9d6f84664ca6e59b6e713ea079118ee',
    'RawText' : 'PENALTIES\nFOR ONE WAY -CO TYPE FARES\n  CANCELLATIONS\n    ANY TIME\n      CANCELLATIONS PERMITTED FOR CANCEL/REFUND.\n         NOTE -\n          NO REFUND PERMITTED IN CASE OF NO-SHOW.\n          --------------------------------------------------\n          TICKET/VALIDITY FORFEIT POLICY-\n          FLIGHT SEGMENT OR SEGMENTS MUST BE CANCELLED ON OR\n          PRIOR TO ORIGINAL DEPARTURE DATE. FAILURE TO\n          CANCEL FLIGHT SEGMENT OR SEGMENTS PRIOR TO\n          DEPARTURE DATE WILL RESULT IN COUPON OR COUPONS\n          FORFEIT AND COUPON WILL HAVE NO VALUE.\n          --------------------------------------------------\n          WAIVED FOR THE DEATH OF THE PASSENGER OR PASSENGER\n          FAMILY MEMBER OR PASSENGERS TRAVELING COMPANION.\n          DOCUMENTATION REQUIRED.\n          IN THE EVENT PSGRS ARE CALLED TO JURY DUTY OR ARE\n          SUBPOENAED FULL REFUND WILL APPLY UPON\n          PRESENTATION OF JURY SUMMONS OR SUBPOENA PROVIDED\n          FLIGHT SEGMENTS ARE CANCELLED PRIOR TO TICKETED\n          DEPARTURE DATE.\n  CHANGES\n    ANY TIME\n      CHANGES PERMITTED.\n         NOTE -\n          NO CHANGES PERMITTED IN CASE OF NO-SHOW.\n          --------------------------------------------------\n          TICKET/VALIDITY FORFEIT POLICY-\n          FLIGHT SEGMENT OR SEGMENTS MUST BE CANCELLED ON OR\n          PRIOR TO ORIGINAL DEPARTURE DATE. FAILURE TO\n          CANCEL FLIGHT SEGMENT OR SEGMENTS PRIOR TO\n          DEPARTURE DATE WILL RESULT IN COUPON OR COUPONS\n          FORFEIT AND COUPON WILL HAVE NO VALUE.\n          FULLY UNUSED TICKETS MAY BE REBOOKED AND TRAVEL\n          COMMENCED UP TO ONE YEAR FROM ORIGINAL ISSUE DATE.\n          PARTIALLY USED TICKET MAY BE REBOOKED AND TRAVEL\n          COMPLETED UP TO ONE YEAR FROM ORIGINAL OUTBOUND\n          DATE.\n          --------------------------------------------------\n          WAIVED FOR THE DEATH OF THE PASSENGER OR PASSENGER\n          FAMILY MEMBER OR PASSENGERS TRAVELING COMPANION.\n          DOCUMENTATION REQUIRED.\n          IN THE EVENT PSGRS ARE CALLED TO JURY DUTY OR ARE\n          SUBPOENAED FULL REFUND WILL APPLY UPON\n          PRESENTATION OF JURY SUMMONS OR SUBPOENA PROVIDED\n          FLIGHT SEGMENTS ARE CANCELLED PRIOR TO TICKETED\n          DEPARTURE DATE.\n          --------------------------------------------------\n          REPRICE USING ANY FARE TYPE EXCEPT ERU AND EOU\n          USED FOR BASIC BRAND.\n          --------------------------------------------------\n          AFTER ISSUANCE OF A TICKET- CHANGES ARE PERMITTED\n          SUBJECT TO THE AVAILABILITY TO BE REBOOKED INTO\n          THE ORIGINAL CLASS OF SERVICE AND MUST MEET ALL\n          CONDITIONS OF THE ORIGINAL FARE INCLUDING ADVANCE\n          RESERVATION AND TICKETING REQUIREMENT.\n          A. NEW BASE FARE AMOUNT MUST BE RECALCULATED\n          USING CURRENT AND APPLICABLE FARES IN EFFECT AT\n          THE DATE OF ISSUANCE\n          B. IF THE NEW FLIGHT SEGMENT IS OF EQUAL OR\n          HIGHER VALUE THE FARE DIFFERENCE MUST BE PAID.\n          C. FLIGHT COUPONS MUST BE USED IN SEQUENCE.\n          CHANGES IN THE SEQUENCE OF ANY FLIGHT COUPON WILL\n          RESULT IN A TICKET REISSUE.\n          -PLEASE NOTE-\n          IF A FARE IS COMBINED WITH HALF A ROUND TRIP FARE\n          IN A CIRCLE TRIP - THE HIGHEST CHANGE FEE AND THE\n          MOST RESTRICTIVE FARE RULE APPLIES TO THE ENTIRE\n          TICKET.\n          --------------------------------------------------\n          TICKET UPGRADE - PASSENGERS UPGRADING TO A\n          BUSINESS CLASS FARE J-/C-/D-/Z-/P- OR PREMIUM\n          ECONOMY FARES O-/E-/N- WILL NOT BE\n          ASSESSED THE CHANGE FEE.\n          ALL OTHER RULES MUST BE MET.\n          ANY NONREFUNDABLE AMOUNT REMAINS NONREFUNDABLE.\nFOR ONE WAY -CO TYPE FARES\n  CANCELLATIONS\n    ANY TIME\n      CANCELLATIONS PERMITTED FOR CANCEL/REFUND.\n         NOTE -\n          NO REFUND PERMITTED IN CASE OF NO-SHOW.\n          --------------------------------------------------\n          TICKET/VALIDITY FORFEIT POLICY-\n          FLIGHT SEGMENT OR SEGMENTS MUST BE CANCELLED ON OR\n          PRIOR TO ORIGINAL DEPARTURE DATE. FAILURE TO\n          CANCEL FLIGHT SEGMENT OR SEGMENTS PRIOR TO\n          DEPARTURE DATE WILL RESULT IN COUPON OR COUPONS\n          FORFEIT AND COUPON WILL HAVE NO VALUE.\n          --------------------------------------------------\n          WAIVED FOR THE DEATH OF THE PASSENGER OR PASSENGER\n          FAMILY MEMBER OR PASSENGERS TRAVELING COMPANION.\n          DOCUMENTATION REQUIRED.\n          IN THE EVENT PSGRS ARE CALLED TO JURY DUTY OR ARE\n          SUBPOENAED FULL REFUND WILL APPLY UPON\n          PRESENTATION OF JURY SUMMONS OR SUBPOENA PROVIDED\n          FLIGHT SEGMENTS ARE CANCELLED PRIOR TO TICKETED\n          DEPARTURE DATE.\n          -------------------------------------------------\n          TICKETS ISSUED ON/BEFORE 27MAY20 ARE NON\n          REFUNDABLE\n  CHANGES\n    ANY TIME\n      CHANGES PERMITTED.\n         NOTE -\n          NO CHANGES PERMITTED IN CASE OF NO-SHOW.\n          --------------------------------------------------\n          TICKET/VALIDITY FORFEIT POLICY-\n          FLIGHT SEGMENT OR SEGMENTS MUST BE CANCELLED ON OR\n          PRIOR TO ORIGINAL DEPARTURE DATE. FAILURE TO\n          CANCEL FLIGHT SEGMENT OR SEGMENTS PRIOR TO\n          DEPARTURE DATE WILL RESULT IN COUPON OR COUPONS\n          FORFEIT AND COUPON WILL HAVE NO VALUE.\n          FULLY UNUSED TICKETS MAY BE REBOOKED AND TRAVEL\n          COMMENCED UP TO ONE YEAR FROM ORIGINAL ISSUE DATE.\n          PARTIALLY USED TICKET MAY BE REBOOKED AND TRAVEL\n          COMPLETED UP TO ONE YEAR FROM ORIGINAL OUTBOUND\n          DATE.\n          --------------------------------------------------\n          WAIVED FOR THE DEATH OF THE PASSENGER OR PASSENGER\n          FAMILY MEMBER OR PASSENGERS TRAVELING COMPANION.\n          DOCUMENTATION REQUIRED.\n          IN THE EVENT PSGRS ARE CALLED TO JURY DUTY OR ARE\n          SUBPOENAED FULL REFUND WILL APPLY UPON\n          PRESENTATION OF JURY SUMMONS OR SUBPOENA PROVIDED\n          FLIGHT SEGMENTS ARE CANCELLED PRIOR TO TICKETED\n          DEPARTURE DATE.\n          --------------------------------------------------\n          REPRICE USING ANY FARE TYPE EXCEPT ERU AND EOU\n          USED FOR BASIC BRAND.\n          --------------------------------------------------\n          AFTER ISSUANCE OF A TICKET- CHANGES ARE PERMITTED\n          SUBJECT TO THE AVAILABILITY TO BE REBOOKED INTO\n          THE ORIGINAL CLASS OF SERVICE AND MUST MEET ALL\n          CONDITIONS OF THE ORIGINAL FARE INCLUDING ADVANCE\n          RESERVATION AND TICKETING REQUIREMENT.\n          A. NEW BASE FARE AMOUNT MUST BE RECALCULATED\n          USING CURRENT AND APPLICABLE FARES IN EFFECT AT\n          THE DATE OF ISSUANCE\n          B. IF THE NEW FLIGHT SEGMENT IS OF EQUAL OR\n          HIGHER VALUE THE FARE DIFFERENCE MUST BE PAID.\n          C. FLIGHT COUPONS MUST BE USED IN SEQUENCE.\n          CHANGES IN THE SEQUENCE OF ANY FLIGHT COUPON WILL\n          RESULT IN A TICKET REISSUE.\n          -PLEASE NOTE-\n          IF A FARE IS COMBINED WITH HALF A ROUND TRIP FARE\n          IN A CIRCLE TRIP - THE HIGHEST CHANGE FEE AND THE\n          MOST RESTRICTIVE FARE RULE APPLIES TO THE ENTIRE\n          TICKET.\n          --------------------------------------------------\n          TICKET UPGRADE - PASSENGERS UPGRADING TO A\n          BUSINESS CLASS FARE J-/C-/D-/Z-/P- OR PREMIUM\n          ECONOMY FARES O-/E-/N- WILL NOT BE\n          ASSESSED THE CHANGE FEE.\n          ALL OTHER RULES MUST BE MET.\n          ANY NONREFUNDABLE AMOUNT REMAINS NONREFUNDABLE.\n          -------------------------------------------------\n          FOR TICKET ISSUED ON/BEFORE 29FEB29 FOR TRAVEL\n          ON/BEFORE 29FEB20 REGULAR FARE RULES WILL APPLY',
    'NormalizedText' : 'PENALTIESFORONEWAY-COTYPEFARESCANCELLATIONSANYTIMECANCELLATIONSPERMITTEDFORCANCEL/REFUND.NOTE-NOREFUNDPERMITTEDINCASEOFNO-SHOW.--------------------------------------------------TICKET/VALIDITYFORFEITPOLICY-FLIGHTSEGMENTORSEGMENTSMUSTBECANCELLEDONORPRIORTOORIGINALDEPARTUREDATE.FAILURETOCANCELFLIGHTSEGMENTORSEGMENTSPRIORTODEPARTUREDATEWILLRESULTINCOUPONORCOUPONSFORFEITANDCOUPONWILLHAVENOVALUE.--------------------------------------------------WAIVEDFORTHEDEATHOFTHEPASSENGERORPASSENGERFAMILYMEMBERORPASSENGERSTRAVELINGCOMPANION.DOCUMENTATIONREQUIRED.INTHEEVENTPSGRSARECALLEDTOJURYDUTYORARESUBPOENAEDFULLREFUNDWILLAPPLYUPONPRESENTATIONOFJURYSUMMONSORSUBPOENAPROVIDEDFLIGHTSEGMENTSARECANCELLEDPRIORTOTICKETEDDEPARTUREDATE.CHANGESANYTIMECHANGESPERMITTED.NOTE-NOCHANGESPERMITTEDINCASEOFNO-SHOW.--------------------------------------------------TICKET/VALIDITYFORFEITPOLICY-FLIGHTSEGMENTORSEGMENTSMUSTBECANCELLEDONORPRIORTOORIGINALDEPARTUREDATE.FAILURETOCANCELFLIGHTSEGMENTORSEGMENTSPRIORTODEPARTUREDATEWILLRESULTINCOUPONORCOUPONSFORFEITANDCOUPONWILLHAVENOVALUE.FULLYUNUSEDTICKETSMAYBEREBOOKEDANDTRAVELCOMMENCEDUPTOONEYEARFROMORIGINALISSUEDATE.PARTIALLYUSEDTICKETMAYBEREBOOKEDANDTRAVELCOMPLETEDUPTOONEYEARFROMORIGINALOUTBOUNDDATE.--------------------------------------------------WAIVEDFORTHEDEATHOFTHEPASSENGERORPASSENGERFAMILYMEMBERORPASSENGERSTRAVELINGCOMPANION.DOCUMENTATIONREQUIRED.INTHEEVENTPSGRSARECALLEDTOJURYDUTYORARESUBPOENAEDFULLREFUNDWILLAPPLYUPONPRESENTATIONOFJURYSUMMONSORSUBPOENAPROVIDEDFLIGHTSEGMENTSARECANCELLEDPRIORTOTICKETEDDEPARTUREDATE.--------------------------------------------------REPRICEUSINGANYFARETYPEEXCEPTERUANDEOUUSEDFORBASICBRAND.--------------------------------------------------AFTERISSUANCEOFATICKET-CHANGESAREPERMITTEDSUBJECTTOTHEAVAILABILITYTOBEREBOOKEDINTOTHEORIGINALCLASSOFSERVICEANDMUSTMEETALLCONDITIONSOFTHEORIGINALFAREINCLUDINGADVANCERESERVATIONANDTICKETINGREQUIREMENT.A.NEWBASEFAREAMOUNTMUSTBERECALCULATEDUSINGCURRENTANDAPPLICABLEFARESINEFFECTATTHEDATEOFISSUANCEB.IFTHENEWFLIGHTSEGMENTISOFEQUALORHIGHERVALUETHEFAREDIFFERENCEMUSTBEPAID.C.FLIGHTCOUPONSMUSTBEUSEDINSEQUENCE.CHANGESINTHESEQUENCEOFANYFLIGHTCOUPONWILLRESULTINATICKETREISSUE.-PLEASENOTE-IFAFAREISCOMBINEDWITHHALFAROUNDTRIPFAREINACIRCLETRIP-THEHIGHESTCHANGEFEEANDTHEMOSTRESTRICTIVEFARERULEAPPLIESTOTHEENTIRETICKET.--------------------------------------------------TICKETUPGRADE-PASSENGERSUPGRADINGTOABUSINESSCLASSFAREJ-/C-/D-/Z-/P-ORPREMIUMECONOMYFARESO-/E-/N-WILLNOTBEASSESSEDTHECHANGEFEE.ALLOTHERRULESMUSTBEMET.ANYNONREFUNDABLEAMOUNTREMAINSNONREFUNDABLE.FORONEWAY-COTYPEFARESCANCELLATIONSANYTIMECANCELLATIONSPERMITTEDFORCANCEL/REFUND.NOTE-NOREFUNDPERMITTEDINCASEOFNO-SHOW.--------------------------------------------------TICKET/VALIDITYFORFEITPOLICY-FLIGHTSEGMENTORSEGMENTSMUSTBECANCELLEDONORPRIORTOORIGINALDEPARTUREDATE.FAILURETOCANCELFLIGHTSEGMENTORSEGMENTSPRIORTODEPARTUREDATEWILLRESULTINCOUPONORCOUPONSFORFEITANDCOUPONWILLHAVENOVALUE.--------------------------------------------------WAIVEDFORTHEDEATHOFTHEPASSENGERORPASSENGERFAMILYMEMBERORPASSENGERSTRAVELINGCOMPANION.DOCUMENTATIONREQUIRED.INTHEEVENTPSGRSARECALLEDTOJURYDUTYORARESUBPOENAEDFULLREFUNDWILLAPPLYUPONPRESENTATIONOFJURYSUMMONSORSUBPOENAPROVIDEDFLIGHTSEGMENTSARECANCELLEDPRIORTOTICKETEDDEPARTUREDATE.-------------------------------------------------TICKETSISSUEDON/BEFORE27MAY20ARENONREFUNDABLECHANGESANYTIMECHANGESPERMITTED.NOTE-NOCHANGESPERMITTEDINCASEOFNO-SHOW.--------------------------------------------------TICKET/VALIDITYFORFEITPOLICY-FLIGHTSEGMENTORSEGMENTSMUSTBECANCELLEDONORPRIORTOORIGINALDEPARTUREDATE.FAILURETOCANCELFLIGHTSEGMENTORSEGMENTSPRIORTODEPARTUREDATEWILLRESULTINCOUPONORCOUPONSFORFEITANDCOUPONWILLHAVENOVALUE.FULLYUNUSEDTICKETSMAYBEREBOOKEDANDTRAVELCOMMENCEDUPTOONEYEARFROMORIGINALISSUEDATE.PARTIALLYUSEDTICKETMAYBEREBOOKEDANDTRAVELCOMPLETEDUPTOONEYEARFROMORIGINALOUTBOUNDDATE.--------------------------------------------------WAIVEDFORTHEDEATHOFTHEPASSENGERORPASSENGERFAMILYMEMBERORPASSENGERSTRAVELINGCOMPANION.DOCUMENTATIONREQUIRED.INTHEEVENTPSGRSARECALLEDTOJURYDUTYORARESUBPOENAEDFULLREFUNDWILLAPPLYUPONPRESENTATIONOFJURYSUMMONSORSUBPOENAPROVIDEDFLIGHTSEGMENTSARECANCELLEDPRIORTOTICKETEDDEPARTUREDATE.--------------------------------------------------REPRICEUSINGANYFARETYPEEXCEPTERUANDEOUUSEDFORBASICBRAND.--------------------------------------------------AFTERISSUANCEOFATICKET-CHANGESAREPERMITTEDSUBJECTTOTHEAVAILABILITYTOBEREBOOKEDINTOTHEORIGINALCLASSOFSERVICEANDMUSTMEETALLCONDITIONSOFTHEORIGINALFAREINCLUDINGADVANCERESERVATIONANDTICKETINGREQUIREMENT.A.NEWBASEFAREAMOUNTMUSTBERECALCULATEDUSINGCURRENTANDAPPLICABLEFARESINEFFECTATTHEDATEOFISSUANCEB.IFTHENEWFLIGHTSEGMENTISOFEQUALORHIGHERVALUETHEFAREDIFFERENCEMUSTBEPAID.C.FLIGHTCOUPONSMUSTBEUSEDINSEQUENCE.CHANGESINTHESEQUENCEOFANYFLIGHTCOUPONWILLRESULTINATICKETREISSUE.-PLEASENOTE-IFAFAREISCOMBINEDWITHHALFAROUNDTRIPFAREINACIRCLETRIP-THEHIGHESTCHANGEFEEANDTHEMOSTRESTRICTIVEFARERULEAPPLIESTOTHEENTIRETICKET.--------------------------------------------------TICKETUPGRADE-PASSENGERSUPGRADINGTOABUSINESSCLASSFAREJ-/C-/D-/Z-/P-ORPREMIUMECONOMYFARESO-/E-/N-WILLNOTBEASSESSEDTHECHANGEFEE.ALLOTHERRULESMUSTBEMET.ANYNONREFUNDABLEAMOUNTREMAINSNONREFUNDABLE.-------------------------------------------------FORTICKETISSUEDON/BEFORE29FEB29FORTRAVELON/BEFORE29FEB20REGULARFARERULESWILLAPPLY',
        'Subrules' : [ 
        {
            'Change' : null,
            'Cancellation' : {
                'FareCombination' : null,
                'Permission' : [ 
                    {
                        'Permitted' : false,
                        'Deadline' : {
                            'NoShow' : true
                        }
                    }, 
                    {
                        'Permitted' : false,
                        'Deadline' : {
                            'DayOfFlight' : true
                        }
                    }, 
                    {
                        'Permitted' : false,
                        'Deadline' : {
                            'DayOfFlight' : true
                        }
                    }, 
                    {
                        'Permitted' : false,
                        'Deadline' : {
                            'DayOfFlight' : true
                        }
                    }, 
                    {
                        'Permitted' : false,
                        'Deadline' : {
                            'DayOfFlight' : true
                        }
                    }, 
                    {
                        'Permitted' : false,
                        'Deadline' : {
                            'NoShow' : true
                        }
                    }, 
                    {
                        'Permitted' : false,
                        'Deadline' : {
                            'DayOfFlight' : true
                        }
                    }, 
                    {
                        'Permitted' : false,
                        'Deadline' : {
                            'DayOfFlight' : true
                        }
                    }, 
                    {
                        'Permitted' : false,
                        'Deadline' : {
                            'DayOfFlight' : true
                        }
                    }, 
                    {
                        'Permitted' : false,
                        'Deadline' : {
                            'DayOfFlight' : true
                        }
                    }
                ]
            }
        }
    ],
    'CreatedAt' : ISODate('2020-06-09T01:51:30.865Z'),
    'UpdatedAt' : ISODate('2020-10-22T14:25:43.167Z'),
    'UpdatedBy' : {
        '_id' : 'auth0|5a1ebd9ed0998b176385f48e',
        'Name' : 'Jekaterina Onufrijeva',
        'Email' : '<EMAIL>',
        'Roles' : [ 
            'system', 
            'rule_validator'
        ]
    },
    'Version' : '2',
    'ReservationsCount' : 17,
    'CancellationsCount' : 17,
    'ChangesCount' : 0,
    'AllowRefunds' : true,
    'AllowChanges' : false,
    'IsValidatedForRefunds' : true,
    'IsValidatedForChanges' : false
}",
        ["416aa077fd0cea26fd9805cc42648bc6"] = @"{
    '_id' : '416aa077fd0cea26fd9805cc42648bc6',
    'RawText' : 'PENALTIES\nUNLESS OTHERWISE SPECIFIED   NOTE - GENERAL RULE DOES NOT\nAPPLY\n  CANCELLATIONS\n    ANY TIME\n      CANCELLATIONS PERMITTED FOR CANCEL/REFUND.\n      TICKET IS NON-REFUNDABLE IN CASE OF NO-SHOW.\n         NOTE -\n          CHARGE USD 300.00 FOR NO-SHOW FOR FULLY UNUSED\n          TICKETS.\n          NIL REFUND FOR PARTIALLY USED OW FARE TICKETS.\n          ---\n  CHANGES\n    ANY TIME\n      CHANGES PERMITTED.\n         NOTE -\n          CHARGE USD 150.00 FOR NO-SHOW.\n          NO NAME CHANGES PERMITTED.\n          ---\n          CHANGES -\n          ... A CHANGE IS A DATE/FLIGHT/ROUTING/BOOKING\n              CODE CHANGE.\n          ... CHANGE FEE APPLIES PER PASSENGER PER\n              TRANSACTION.\n          ... CHANGE FEE DOES NOT APPLY TO INFANT NOT\n              OCCUPYING A SEAT.\n          ... CHD/INF WITH A SEAT DISCOUNT DOES NOT APPLY\n              ON THE CHANGE/REROUTING FEES.\n          ... CHANGE IS PERMITTED WITHIN TICKET VALIDITY OF\n              ORIGINAL TICKET.\n          ... CHANGES ONLY PERMITTED TO FARE\n              OF EQUIVALENT OR HIGHER VALUE.\n          --------------------------------------------------\n          WAIVERS\n          1.WAIVED FOR DEATH OF PASSENGER OR FAMILY MEMBER.\n            A COPY OF VALID DEATH CERTIFICATE ISSUED BY A\n            COMPETENT MEDICAL AUTHORITY IS REQUIRED.\n            FAMILY MEMBERS AS DEFINED IN EK CONDITIONS OF\n            CARRIAGE OR PASSENGER AIRLINE TARIFF RULE BOOK.\n          2.NO WAIVER APPLICABLE FOR ILLNESS OF PASSENGER\n            OR FAMILY MEMBER.\n          3.CONTACT EK OFFICE FOR WAIVERS DEFINED ABOVE.\n          --------------------------------------------------\n          CHANGES AGAINST NO SHOW\n          - A NO-SHOW FOR A FLIGHT IS CONSIDERED WHEN A\n          PASSENGER FAILS TO USE THE RESERVATION ONE HOUR\n          BEFORE DEPARTURE OF THE SCHEDULED\n          FLIGHT.\n          - FAILURE TO OCCUPY A RESERVED SEAT ON ANY\n          SEGMENT OF THE ITINERARY WILL RESULT IN ALL\n          SUBSEQUENT SEGMENTS OF THE ITINERARY BEING\n          CANCELLED.IN SUCH CASES NO-SHOW FEE WILL\n          APPLY.\n          - IN CASE OF NO-SHOW ONLY ONE FEE IS TO BE CHARGED\n          I.E. EITHER THE NO-SHOW FEE OR THE CHANGES FEE\n          WHICHEVER IS HIGHER AND NOT BOTH.\n          --------------------------------------------------\n          UPGRADES - APPLICABLE ONLY IF CHANGES\n          ARE\n          PERMITTED.\n          1.UPGRADES TO HIGHER FARE IN THE SAME CABIN.\n            RECALCULATE THE FARE FROM THE POINT OF ORIGIN\n            PROVIDED THE FARE RULE CONDITIONS OF THE\n            HIGHER FARE ARE MET.\n            COLLECT THE FARE DIFFERENCE AND CHANGE FEE\n            APPLIES PER PASSENGER PER TRANSACTION.\n            IF THE UPGRADED TICKET IS SUBSEQUENTLY\n            CANCELLED THE ORIGINAL CHARGE WILL APPLY.\n          2.UPGRADES TO HIGHER FARE IN A HIGHER CABIN.\n            RECALCULATE THE FARE FROM THE POINT OF ORIGIN\n            PROVIDED THE FARE RULE CONDITIONS OF THE HIGHER\n            FARE ARE MET.\n            COLLECT THE FARE DIFFERENCE. CHANGE FEE IS\n            WAIVED FOR UPGRADE TO HIGHER CABIN.\n            IF THE UPGRADED TICKET IS SUBSEQUENTLY\n            CANCELLED THE ORIGINAL CHARGE WILL APPLY.\n          --------------------------------------------------\n          VOLUNTARY DOWNGRADE - NO REFUNDS IN CASE OF\n          VOLUNTARY DOWNGRADE.\n          --------------------------------------------------\n          PENALTY FEE APPLICATION\n          1.ANY TIME WHEN THIS FARE IS COMBINED WITH\n            ANOTHER FARE AND ONLY ONE FARE COMPONENT IS\n            CHANGED THE PENALTY CONDITIONS OF THE CHANGED\n            FARE COMPONENT WILL APPLY.\n          2.ANY TIME WHEN MORE THAN ONE FARE COMPONENT IS\n            BEING CHANGED THE HIGHEST PENALTY OF ALL\n            CHANGED FARE COMPONENTS WILL APPLY.\n          --------------------------------------------------\n          REPRICING CONDITIONS\n          A.BEFORE DEPARTURE / FULLY UNUTILISED TICKETS\n              IN THE EVENT OF VOLUNTARY CHANGES TO ANY\n              FLIGHT/DATE ON THE ITINERARY TICKET HAS TO BE\n              REISSUED TO FARE OF EQUIVALENT OR HIGHER VALUE\n              AND COLLECT ANY FARE DIFFERENCE AS AN ADC. THE\n              FARES FOR THE PASSENGER JOURNEY SHALL BE\n              RECALCULATED FROM THE POINT OF ORIGIN BASED\n              ON THE DATE OF REISSUE.CHANGE FEES IF ANY TO\n              BE COLLECTED AS PER THE ORIGINAL FARE PAID\n              AND SHOWN ON TICKET AS AN OB TAX PLUS\n           ANY ADDITIONAL TAXES.\n          B.AFTER DEPARTURE / PARTIALLY UTILISED TICKETS\n            AFTER COMMENCEMENT OF THE FIRST SECTOR OF THE\n            JOURNEY OR THE JOURNEY PERFORMED TILL THE\n            TURNAROUND / FARE BREAK POINT.\n              IN THE EVENT OF VOLUNTARY CHANGES AFTER\n              COMMENCEMENT OF TRAVEL THE FARES FOR THE\n              PASSENGER JOURNEY SHALL BE RECALCULATED FROM\n              THE POINT OF ORIGIN IN ACCORDANCE WITH THE\n              FARES IN EFFECT ON THE DATE OF ORIGINAL\n              ISSUED TICKET AND COLLECT ANY FARE DIFFERENCE\n          AS AN ADC PLUS THE APPLICABLE CHANGE FEE FOR THE\n          TICKETED FARE AS OB TAX PLUS ANY ADDITIONAL TAXES\n          ON THE NEW TICKET. NEW TICKET HAS TO BE ISSUED TO\n          FARE OF EQUIVALENT OR HIGHER VALUE\n          --------------------------------------------------\n          CANCELLATION / REFUNDS\n          ... CANCELLATION / REFUND FEES ARE NOT\n              COMMISSIONABLE.\n          ... CANCELLATION FEE DOES NOT APPLY TO INFANT NOT\n              OCCUPYING A SEAT.\n          --------------------------------------------------\n          WAIVERS\n          1.WAIVED FOR DEATH OF PASSENGER OR FAMILY MEMBER.\n            A COPY OF VALID DEATH CERTIFICATE ISSUED BY A\n            COMPETENT MEDICAL AUTHORITY IS REQUIRED.\n            FAMILY MEMBERS AS DEFINED IN EK CONDITIONS OF\n            CARRIAGE OR PASSENGER AIRLINE TARIFF RULE\n            BOOK.\n          2.NO WAIVER APPLICABLE FOR ILLNESS OF PASSENGER\n            OR FAMILY MEMBER.\n          3.CONTACT EK LOCAL OFFICE FOR WAIVERS DEFINED\n            ABOVE.\n          --------------------------------------------------\n          CANCELLATION / REFUNDS AGAINST NO SHOW.\n          ... A NO-SHOW FOR A FLIGHT IS CONSIDERED WHEN A\n              PASSENGER FAILS TO USE THE RESERVATION ONE\n              HOUR BEFORE DEPARTURE OF THE SCHEDULED\n              FLIGHT.\n          ... FAILURE TO OCCUPY A RESERVED SEAT ON ANY\n              SEGMENT OF THE ITINERARY WILL RESULT IN ALL\n              SUBSEQUENT SEGMENTS OF THE ITINERARY BEING\n              CANCELLED. IN SUCH CASES ONLY NO-SHOW FEE\n              WILL APPLY AND NOT BOTH.\n          ... NO SHOW  FEE IS NON COMMISSIONABLE.\n          --------------------------------------------------\n          CANCELLATION / REFUNDS AGAINST UPGRADES -\n          IF THE UPGRADED TICKET IS SUBSEQUENTLY CANCELLED\n          THE ORIGINAL CHARGE WILL APPLY.\n          --------------------------------------------------\n          OUT OF SEQUENCE TICKETS -\n          ANYTIME TICKETS IS UTILIZED OUT OF SEQUENCE NO\n          REFUND OF FARE AND THE CARRIER IMPOSED SURCHARGE -\n          YQ.\n          --------------------------------------------------\n          A.WHEN OUTBOUND AND INBOUND FARES ARE REFUNDABLE.\n            WHEN COMBINING FARES THAT HAVE CANCELLATION\n            FEES THE HIGHEST CANCELLATION FEE OF EACH\n            CANCELLED PRICING UNIT APPLIES.\n           A1.BEFORE DEPARTURE / FULLY UNUTILISED TICKETS\n          ... DEDUCT THE APPLICABLE HIGHEST CANCELLATION\n              FEE FROM THE TOTAL OF THE BASE FARE AND\n              CARRIER IMPOSED SURCHARGE - YQ.\n          ... REFUND THE RESIDUAL AMOUNT ALONG WITH THE\n              REFUNDABLE GOVERNMENT TAXES.\n           A2.AFTER DEPARTURE / PARTIALLY UTILISED TICKETS -\n              AFTER COMMENCEMENT OF THE FIRST SECTOR OF THE\n              JOURNEY.\n          ... DEDUCT THE OW FARE OF EQUAL OR HIGHER\n              AMOUNT THAN THE FARE PAID FOR THE PORTION OF\n              THE JOURNEY PERFORMED IN THE SAME OR NEXT\n              HIGHER RBD.\n          ... COLLECT APPLICABLE CANCELLATION FEE AND\n              THE CARRIER IMPOSED SURCHARGE - YQ FOR THE\n              JOURNEY PERFORMED. DEDUCT NON-REFUNDABLE\n              TAXES.\n          ... REFUND THE CARRIER IMPOSED SURCHARGE - YQ AND\n              UNUTILISED GOVERNMENT TAXES FOR THE PORTION\n              OF THE JOURNEY NOT PERFORMED.\n          ... NO REFUND OF FARE AND CARRIER IMPOSED FEES -\n              YQ IF THE UTILISED OW FARE IS GREATER THAN\n              THE TICKETED FARE.\n          ... NO REFUND OF FARE AND CARRIER IMPOSED\n              SURCHARGE - YQ IF JOURNEY PERFORMED BEYOND\n              THE TURNAROUND/FARE BREAK POINT.\n          --------------------------------------------------\n          B.WHEN OUTBOUND AND INBOUND FARES ARE NON -\n            REFUNDABLE.\n          ... NO REFUND OF THE FARE AND CARRIER IMPOSED\n              FEES - YQ.\n          ... DEDUCT NON-REFUNDABLE TAXES.\n          ... IF NON- REFUNDABLE FARES REISSUED TO A\n              REFUNDABLE FARE THE ORIGINAL PAID FARE AND\n              CARRIER IMPOSED FEES - YQ WILL BE NON-\n              REFUNDABLE.\n          --------------------------------------------------\n          C.COMBINATION OF REFUNDABLE AND NON-REFUNDABLE\n            FARES.\n            1.BEFORE DEPARTURE/FULLY UNUTILISED TICKETS.\n          ... DEDUCT THE NON-REFUNDABLE FARE PAID AND THE\n              CANCELLATION FEE OF THE REFUNDABLE FARE.\n          ... DEDUCT NON-REFUNDABLE TAXES.\n          ... CARRIER IMPOSED FEES - YQ ARE NOT\n              REFUNDABLE.\n            2.AFTER DEPARTURE / PARTIALLY UTILISED TICKETS -\n              AFTER COMMENCEMENT OF THE FIRST SECTOR OF THE\n              JOURNEY.\n            2.1 IF OUTBOUND FARE COMPONENT IS NON-\n              REFUNDABLE.\n          ..... DEDUCT THE OW FARE OF EQUAL OR HIGHER\n                AMOUNT THAN THE FARE PAID FOR THE PORTION\n                OF THE JOURNEY PERFORMED IN THE SAME OR\n                NEXT HIGHER RBD.\n          ..... COLLECT THE CANCELLATION FEE OF THE\n                REFUNDABLE FARE.\n          ..... DEDUCT NON-REFUNDABLE TAXES.\n          ..... NO REFUND OF FARE AND CARRIER IMPOSED FEES -\n                YQ IF THE UTILISED OW FARE IS GREATER THAN\n                THE TICKETED FARE.\n          ..... CARRIER IMPOSED FEES YQ ARE NOT\n                REFUNDABLE.\n            2.2 IF INBOUND FARE COMPONENT IS NON-REFUNDABLE.\n          ..... NO REFUND OF THE FARE AND CARRIER IMPOSED\n                FEES - YQ.\n          ..... DEDUCT NON-REFUNDABLE TAXES.\n          ..... NO REFUND OF FARE AND CARRIER IMPOSED\n                SURCHARGE - YQ IF JOURNEY PERFORMED BEYOND\n                THE TURNAROUND/FARE BREAK POINT.\n          --------------------------------------------------\n          FOR FULLY UNUTILISED TICKETS YR/YQ CAN BE REFUNDED\n          FOR PARTIALLY UNUTILISED TICKETS YR/YQ CANNOT BE\n          REFUNDED.\n          ------------------------------------------------',
    'NormalizedText' : 'PENALTIESUNLESSOTHERWISESPECIFIEDNOTE-GENERALRULEDOESNOTAPPLYCANCELLATIONSANYTIMECANCELLATIONSPERMITTEDFORCANCEL/REFUND.TICKETISNON-REFUNDABLEINCASEOFNO-SHOW.NOTE-CHARGEUSD300.00FORNO-SHOWFORFULLYUNUSEDTICKETS.NILREFUNDFORPARTIALLYUSEDOWFARETICKETS.---CHANGESANYTIMECHANGESPERMITTED.NOTE-CHARGEUSD150.00FORNO-SHOW.NONAMECHANGESPERMITTED.---CHANGES-...ACHANGEISADATE/FLIGHT/ROUTING/BOOKINGCODECHANGE....CHANGEFEEAPPLIESPERPASSENGERPERTRANSACTION....CHANGEFEEDOESNOTAPPLYTOINFANTNOTOCCUPYINGASEAT....CHD/INFWITHASEATDISCOUNTDOESNOTAPPLYONTHECHANGE/REROUTINGFEES....CHANGEISPERMITTEDWITHINTICKETVALIDITYOFORIGINALTICKET....CHANGESONLYPERMITTEDTOFAREOFEQUIVALENTORHIGHERVALUE.--------------------------------------------------WAIVERS1.WAIVEDFORDEATHOFPASSENGERORFAMILYMEMBER.ACOPYOFVALIDDEATHCERTIFICATEISSUEDBYACOMPETENTMEDICALAUTHORITYISREQUIRED.FAMILYMEMBERSASDEFINEDINEKCONDITIONSOFCARRIAGEORPASSENGERAIRLINETARIFFRULEBOOK.2.NOWAIVERAPPLICABLEFORILLNESSOFPASSENGERORFAMILYMEMBER.3.CONTACTEKOFFICEFORWAIVERSDEFINEDABOVE.--------------------------------------------------CHANGESAGAINSTNOSHOW-ANO-SHOWFORAFLIGHTISCONSIDEREDWHENAPASSENGERFAILSTOUSETHERESERVATIONONEHOURBEFOREDEPARTUREOFTHESCHEDULEDFLIGHT.-FAILURETOOCCUPYARESERVEDSEATONANYSEGMENTOFTHEITINERARYWILLRESULTINALLSUBSEQUENTSEGMENTSOFTHEITINERARYBEINGCANCELLED.INSUCHCASESNO-SHOWFEEWILLAPPLY.-INCASEOFNO-SHOWONLYONEFEEISTOBECHARGEDI.E.EITHERTHENO-SHOWFEEORTHECHANGESFEEWHICHEVERISHIGHERANDNOTBOTH.--------------------------------------------------UPGRADES-APPLICABLEONLYIFCHANGESAREPERMITTED.1.UPGRADESTOHIGHERFAREINTHESAMECABIN.RECALCULATETHEFAREFROMTHEPOINTOFORIGINPROVIDEDTHEFARERULECONDITIONSOFTHEHIGHERFAREAREMET.COLLECTTHEFAREDIFFERENCEANDCHANGEFEEAPPLIESPERPASSENGERPERTRANSACTION.IFTHEUPGRADEDTICKETISSUBSEQUENTLYCANCELLEDTHEORIGINALCHARGEWILLAPPLY.2.UPGRADESTOHIGHERFAREINAHIGHERCABIN.RECALCULATETHEFAREFROMTHEPOINTOFORIGINPROVIDEDTHEFARERULECONDITIONSOFTHEHIGHERFAREAREMET.COLLECTTHEFAREDIFFERENCE.CHANGEFEEISWAIVEDFORUPGRADETOHIGHERCABIN.IFTHEUPGRADEDTICKETISSUBSEQUENTLYCANCELLEDTHEORIGINALCHARGEWILLAPPLY.--------------------------------------------------VOLUNTARYDOWNGRADE-NOREFUNDSINCASEOFVOLUNTARYDOWNGRADE.--------------------------------------------------PENALTYFEEAPPLICATION1.ANYTIMEWHENTHISFAREISCOMBINEDWITHANOTHERFAREANDONLYONEFARECOMPONENTISCHANGEDTHEPENALTYCONDITIONSOFTHECHANGEDFARECOMPONENTWILLAPPLY.2.ANYTIMEWHENMORETHANONEFARECOMPONENTISBEINGCHANGEDTHEHIGHESTPENALTYOFALLCHANGEDFARECOMPONENTSWILLAPPLY.--------------------------------------------------REPRICINGCONDITIONSA.BEFOREDEPARTURE/FULLYUNUTILISEDTICKETSINTHEEVENTOFVOLUNTARYCHANGESTOANYFLIGHT/DATEONTHEITINERARYTICKETHASTOBEREISSUEDTOFAREOFEQUIVALENTORHIGHERVALUEANDCOLLECTANYFAREDIFFERENCEASANADC.THEFARESFORTHEPASSENGERJOURNEYSHALLBERECALCULATEDFROMTHEPOINTOFORIGINBASEDONTHEDATEOFREISSUE.CHANGEFEESIFANYTOBECOLLECTEDASPERTHEORIGINALFAREPAIDANDSHOWNONTICKETASANOBTAXPLUSANYADDITIONALTAXES.B.AFTERDEPARTURE/PARTIALLYUTILISEDTICKETSAFTERCOMMENCEMENTOFTHEFIRSTSECTOROFTHEJOURNEYORTHEJOURNEYPERFORMEDTILLTHETURNAROUND/FAREBREAKPOINT.INTHEEVENTOFVOLUNTARYCHANGESAFTERCOMMENCEMENTOFTRAVELTHEFARESFORTHEPASSENGERJOURNEYSHALLBERECALCULATEDFROMTHEPOINTOFORIGININACCORDANCEWITHTHEFARESINEFFECTONTHEDATEOFORIGINALISSUEDTICKETANDCOLLECTANYFAREDIFFERENCEASANADCPLUSTHEAPPLICABLECHANGEFEEFORTHETICKETEDFAREASOBTAXPLUSANYADDITIONALTAXESONTHENEWTICKET.NEWTICKETHASTOBEISSUEDTOFAREOFEQUIVALENTORHIGHERVALUE--------------------------------------------------CANCELLATION/REFUNDS...CANCELLATION/REFUNDFEESARENOTCOMMISSIONABLE....CANCELLATIONFEEDOESNOTAPPLYTOINFANTNOTOCCUPYINGASEAT.--------------------------------------------------WAIVERS1.WAIVEDFORDEATHOFPASSENGERORFAMILYMEMBER.ACOPYOFVALIDDEATHCERTIFICATEISSUEDBYACOMPETENTMEDICALAUTHORITYISREQUIRED.FAMILYMEMBERSASDEFINEDINEKCONDITIONSOFCARRIAGEORPASSENGERAIRLINETARIFFRULEBOOK.2.NOWAIVERAPPLICABLEFORILLNESSOFPASSENGERORFAMILYMEMBER.3.CONTACTEKLOCALOFFICEFORWAIVERSDEFINEDABOVE.--------------------------------------------------CANCELLATION/REFUNDSAGAINSTNOSHOW....ANO-SHOWFORAFLIGHTISCONSIDEREDWHENAPASSENGERFAILSTOUSETHERESERVATIONONEHOURBEFOREDEPARTUREOFTHESCHEDULEDFLIGHT....FAILURETOOCCUPYARESERVEDSEATONANYSEGMENTOFTHEITINERARYWILLRESULTINALLSUBSEQUENTSEGMENTSOFTHEITINERARYBEINGCANCELLED.INSUCHCASESONLYNO-SHOWFEEWILLAPPLYANDNOTBOTH....NOSHOWFEEISNONCOMMISSIONABLE.--------------------------------------------------CANCELLATION/REFUNDSAGAINSTUPGRADES-IFTHEUPGRADEDTICKETISSUBSEQUENTLYCANCELLEDTHEORIGINALCHARGEWILLAPPLY.--------------------------------------------------OUTOFSEQUENCETICKETS-ANYTIMETICKETSISUTILIZEDOUTOFSEQUENCENOREFUNDOFFAREANDTHECARRIERIMPOSEDSURCHARGE-YQ.--------------------------------------------------A.WHENOUTBOUNDANDINBOUNDFARESAREREFUNDABLE.WHENCOMBININGFARESTHATHAVECANCELLATIONFEESTHEHIGHESTCANCELLATIONFEEOFEACHCANCELLEDPRICINGUNITAPPLIES.A1.BEFOREDEPARTURE/FULLYUNUTILISEDTICKETS...DEDUCTTHEAPPLICABLEHIGHESTCANCELLATIONFEEFROMTHETOTALOFTHEBASEFAREANDCARRIERIMPOSEDSURCHARGE-YQ....REFUNDTHERESIDUALAMOUNTALONGWITHTHEREFUNDABLEGOVERNMENTTAXES.A2.AFTERDEPARTURE/PARTIALLYUTILISEDTICKETS-AFTERCOMMENCEMENTOFTHEFIRSTSECTOROFTHEJOURNEY....DEDUCTTHEOWFAREOFEQUALORHIGHERAMOUNTTHANTHEFAREPAIDFORTHEPORTIONOFTHEJOURNEYPERFORMEDINTHESAMEORNEXTHIGHERRBD....COLLECTAPPLICABLECANCELLATIONFEEANDTHECARRIERIMPOSEDSURCHARGE-YQFORTHEJOURNEYPERFORMED.DEDUCTNON-REFUNDABLETAXES....REFUNDTHECARRIERIMPOSEDSURCHARGE-YQANDUNUTILISEDGOVERNMENTTAXESFORTHEPORTIONOFTHEJOURNEYNOTPERFORMED....NOREFUNDOFFAREANDCARRIERIMPOSEDFEES-YQIFTHEUTILISEDOWFAREISGREATERTHANTHETICKETEDFARE....NOREFUNDOFFAREANDCARRIERIMPOSEDSURCHARGE-YQIFJOURNEYPERFORMEDBEYONDTHETURNAROUND/FAREBREAKPOINT.--------------------------------------------------B.WHENOUTBOUNDANDINBOUNDFARESARENON-REFUNDABLE....NOREFUNDOFTHEFAREANDCARRIERIMPOSEDFEES-YQ....DEDUCTNON-REFUNDABLETAXES....IFNON-REFUNDABLEFARESREISSUEDTOAREFUNDABLEFARETHEORIGINALPAIDFAREANDCARRIERIMPOSEDFEES-YQWILLBENON-REFUNDABLE.--------------------------------------------------C.COMBINATIONOFREFUNDABLEANDNON-REFUNDABLEFARES.1.BEFOREDEPARTURE/FULLYUNUTILISEDTICKETS....DEDUCTTHENON-REFUNDABLEFAREPAIDANDTHECANCELLATIONFEEOFTHEREFUNDABLEFARE....DEDUCTNON-REFUNDABLETAXES....CARRIERIMPOSEDFEES-YQARENOTREFUNDABLE.2.AFTERDEPARTURE/PARTIALLYUTILISEDTICKETS-AFTERCOMMENCEMENTOFTHEFIRSTSECTOROFTHEJOURNEY.2.1IFOUTBOUNDFARECOMPONENTISNON-REFUNDABLE......DEDUCTTHEOWFAREOFEQUALORHIGHERAMOUNTTHANTHEFAREPAIDFORTHEPORTIONOFTHEJOURNEYPERFORMEDINTHESAMEORNEXTHIGHERRBD......COLLECTTHECANCELLATIONFEEOFTHEREFUNDABLEFARE......DEDUCTNON-REFUNDABLETAXES......NOREFUNDOFFAREANDCARRIERIMPOSEDFEES-YQIFTHEUTILISEDOWFAREISGREATERTHANTHETICKETEDFARE......CARRIERIMPOSEDFEESYQARENOTREFUNDABLE.2.2IFINBOUNDFARECOMPONENTISNON-REFUNDABLE......NOREFUNDOFTHEFAREANDCARRIERIMPOSEDFEES-YQ......DEDUCTNON-REFUNDABLETAXES......NOREFUNDOFFAREANDCARRIERIMPOSEDSURCHARGE-YQIFJOURNEYPERFORMEDBEYONDTHETURNAROUND/FAREBREAKPOINT.--------------------------------------------------FORFULLYUNUTILISEDTICKETSYR/YQCANBEREFUNDEDFORPARTIALLYUNUTILISEDTICKETSYR/YQCANNOTBEREFUNDED.------------------------------------------------',
    'Subrules' : [ 
        {
            'NoShow' : {
                'Definition' : {
                    'Value' : 1.0,
                    'Units' : 'Hours'
                },
                'Calculation' : 'Only'
            },
            'Change' : {
                'AllFareComponentsAffected' : false,
                'NewPriceShouldBe' : 'EqualOrHigher',
                'UpgradeCabinClassFree' : true,
                'Repricing' : [
                    {
                        'UseHistoricalPrice' : false,
                        'Condition' : {
                            'AfterDeparture' : false
                        }
                    },
                    {
                        'UseHistoricalPrice' : true,
                        'Condition' : {
                            'AfterDeparture' : true
                        }
                    }
                ],
                'ChangeFeeTax' : 'OB',
                'FareCombination' : 'Strictest',
                'Fee' : {
                    'Values': [
                        {
                            'Value': 150,
                            'Ccy': 'USD',
                            'Deadline': {
                                'NoShow': true
                            }
                        }
                    ]
                },
                'FeePer' : [
                    {
                        'Method': 'Transaction'
                    }
                ]
            },
            'Cancellation' : {
                'FareCombination' : 'Strictest',
                'Permission' : [ 
                    {
                        'Permitted' : false,
                        'Deadline' : {
                            'NoShow' : true
                        }
                    }, 
                    {
                        'Permitted' : false,
                        'Deadline' : null,
                        'Condition' : {
                            'AfterDeparture' : true
                        }
                    }
                ],
                'Taxes' : [ 
                    {
                        'Name' : 'YR',
                        'Condition' : {
                            'AfterDeparture' : true
                        }
                    }, 
                    {
                        'Name' : 'YQ',
                        'Condition' : {
                            'AfterDeparture' : true
                        }
                    }
                ],
                'ResidualAmount' : {
                    'Ignore' : false,
                    'DeductFrom' : [ 
                        'YQ'
                    ]
                }
            }
        }
    ],
    'CreatedAt' : ISODate('2020-10-01T07:13:21.946Z'),
    'UpdatedAt' : ISODate('2020-10-13T09:54:18.490Z'),
    'UpdatedBy' : {
        '_id' : 'auth0|5a1ebd9ed0998b176385f48e',
        'Name' : 'Jekaterina Onufrijeva',
        'Email' : '<EMAIL>',
        'Roles' : [ 
            'system', 
            'rule_validator'
        ]
    },
    'Version' : '2',
    'ReservationsCount' : 410,
    'CancellationsCount' : 270,
    'ChangesCount' : 0,
    'AllowRefunds' : true,
    'AllowChanges' : true,
    'IsValidatedForRefunds' : true,
    'IsValidatedForChanges' : true
}"
    };

    private async Task InsertTestFareRulesAsync()
    {
        try
        {
            var fareRules = _database.GetCollection<BsonDocument>(FareRulesQueries.FareRulesCollectionName);

            foreach (string id in FareRules.Keys)
            {
                var document = BsonSerializer.Deserialize<BsonDocument>(FareRules[id]);
                await fareRules.ReplaceOneAsync(
                    new BsonDocument("_id", id),
                    options: new ReplaceOptions { IsUpsert = true },
                    replacement: document);
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error in InsertTestFareRulesAsync()");
        }
    }
}