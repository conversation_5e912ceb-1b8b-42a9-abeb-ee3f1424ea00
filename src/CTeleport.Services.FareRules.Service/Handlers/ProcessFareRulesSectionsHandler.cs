using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.FareRules;
using CTeleport.Services.FareRules.Service.Repositories.Models;
using CTeleport.Services.FareRules.Service.Services.Interfaces;
using Serilog;

namespace CTeleport.Services.FareRules.Service.Handlers;

public class ProcessFareRulesSectionsHandler : ICommandHandler<ProcessFareRulesSections>
{
    private readonly IHandlerFactory _handlerFactory;
    private readonly IFareRulesService _fareRulesService;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    
    public ProcessFareRulesSectionsHandler(
        IHandlerFactory handlerFactory, 
        IFareRulesService fareRulesService,
        IMapper mapper,
        ILogger logger)
    {
        _handlerFactory = handlerFactory;
        _fareRulesService = fareRulesService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task HandleAsync(ProcessFareRulesSections cmd)
    {
        _logger.Information("Start processing {ProcessFareRulesSectionsName}: {Command}", 
            nameof(ProcessFareRulesSections), 
            cmd);

        await _handlerFactory
            .Create(cmd)
            .Run(async () =>
            {
                var sections = _mapper.Map<List<FareRuleSectionEntity>>(cmd.FareRulesSections);
                await _fareRulesService.PersistFareRuleSectionsAsync(sections);
            })
            .OnSuccess(() =>
            {
                _logger.Information($"{nameof(ProcessFareRulesSections)} successfully processed");
                return Task.CompletedTask;
            })
            .OnError(ex =>
            {
                _logger.Error(ex, "Error occured while handling {ProcessFareRulesSectionsName}: {@SerializeObject}", 
                    nameof(ProcessFareRulesSections), 
                    cmd);
                return Task.CompletedTask;
            })
            .ExecuteAsync();
    }
}
