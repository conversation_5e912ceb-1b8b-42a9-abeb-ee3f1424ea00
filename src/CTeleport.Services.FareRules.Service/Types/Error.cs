using System;
using System.Collections.Generic;
using FluentValidation.Results;

namespace CTeleport.Services.FareRules.Service.Types;

public record Error(string Message)
{
    public Exception Exception { get; init; }
};

public sealed record DuplicateFoundError() : Error("Duplicate record found");
public sealed record DatabaseReadError() : <PERSON>rror("Database read error");
public sealed record DatabaseWriteError() : <PERSON>rror("Database write error");
public sealed record NotFoundError() : <PERSON>rror("Record not found");

public sealed record ValidationError(IDictionary<string, string[]> Errors) : Error("Request is invalid");
