FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build

ENV NUGET_XMLDOC_MODE=none
ARG GITHUB_NUGET_REGISTRY_USERNAME
ARG GITHUB_NUGET_REGISTRY_PASSWORD

COPY NuGet.Config .
COPY src/*.sln ./src/

# Copy the main source project files
COPY src/*/*.csproj ./src/
RUN for file in $(ls ./src/*.csproj); do mkdir -p ${file%.*}/ && mv $file ${file%.*}/; done

# Copy the test project files
COPY tests/*/*.csproj ./tests/
RUN for file in $(ls ./tests/*.csproj); do mkdir -p /${file%.*}/ && mv $file /${file%.*}/; done

RUN dotnet restore ./src/CTeleport.Services.FareRules.sln --no-cache

COPY src/ ./src/
COPY tests/ ./tests/
WORKDIR /src
RUN dotnet build "CTeleport.Services.FareRules.Api/CTeleport.Services.FareRules.Api.csproj" -c Release --no-restore

FROM build AS testrunner
WORKDIR /
ENV TEAMCITY_VERSION=2018.1
RUN dotnet test /src/CTeleport.Services.FareRules.sln --no-restore

FROM testrunner AS publish
RUN dotnet publish "/src/CTeleport.Services.FareRules.Api/CTeleport.Services.FareRules.Api.csproj" -c Release -o /app --no-build --no-restore

FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS runtime
RUN apk --no-cache add curl
WORKDIR /app
COPY --from=publish /app .
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 CMD curl --fail -s http://localhost:5045/healthcheck || exit 1
ENV ASPNETCORE_URLS http://*:5045
ENV ASPNETCORE_ENVIRONMENT production
ENTRYPOINT ["dotnet", "CTeleport.Services.FareRules.Api.dll"]
