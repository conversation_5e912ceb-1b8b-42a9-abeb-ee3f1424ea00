<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>disable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\CTeleport.Services.FareRules.Service\CTeleport.Services.FareRules.Service.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="AutoFixture" Version="4.18.0" />
      <PackageReference Include="AutoFixture.AutoMoq" Version="4.18.0" />
      <PackageReference Include="AutoFixture.Xunit2" Version="4.18.0" />
      <PackageReference Include="FluentAssertions" Version="6.12.0" />
      <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.7.2" />
      <PackageReference Include="Mongo2Go" Version="3.1.3" />
      <PackageReference Include="TeamCity.VSTest.TestAdapter" Version="1.0.37" />
      <PackageReference Include="xunit" Version="2.5.1" />
      <PackageReference Include="xunit.runner.visualstudio" Version="2.5.1">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <Resource Include="Data\GetFareRules16AsyncTestsData.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Resource>
    </ItemGroup>

    <ItemGroup>
      <None Update="Data\GetFareRules16AsyncTestsData.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>
</Project>
