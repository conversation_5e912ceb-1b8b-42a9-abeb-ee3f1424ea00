<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>disable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\CTeleport.Services.FareRules.Api\CTeleport.Services.FareRules.Api.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.7.2" />
      <PackageReference Include="TeamCity.VSTest.TestAdapter" Version="1.0.37" />
      <PackageReference Include="xunit" Version="2.5.1" />
      <PackageReference Include="xunit.runner.visualstudio" Version="2.5.1">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
    </ItemGroup>

</Project>
